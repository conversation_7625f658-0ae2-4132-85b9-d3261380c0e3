{"uuid": "3dcc814b-323a-4014-9f4b-e42c6594d109", "name": "should display footer information", "historyId": "3742aab49d1a83d263641b8e6a4ecc9d:96797789f4669ba5e9de53cebde9126b", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Chrome"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "home.test.ts"}, {"name": "titlePath", "value": " > Mobile Chrome > home.test.ts > RASP Home Page Tests - Basic"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-36364-worker-0"}, {"name": "parentSuite", "value": "Mobile Chrome"}, {"name": "subSuite", "value": "RASP Home Page Tests - Basic"}], "links": [], "start": 1761577068547, "testCaseId": "3742aab49d1a83d263641b8e6a4ecc9d", "fullName": "home.test.ts:127:7", "titlePath": ["home.test.ts", "RASP Home Page Tests - Basic"], "stop": 1761577068547}