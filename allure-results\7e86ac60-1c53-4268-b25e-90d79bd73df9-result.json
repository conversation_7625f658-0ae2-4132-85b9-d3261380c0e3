{"uuid": "7e86ac60-1c53-4268-b25e-90d79bd73df9", "name": "should load EAP application form @smoke", "historyId": "e9d8af61ba8124fb85c5e869b230719a:5bd835b0d6b1d4ada3b9f0db936e82c8", "status": "failed", "statusDetails": {"message": "TypeError: Cannot read properties of null (reading 'newContext')", "trace": "TypeError: Cannot read properties of null (reading 'newContext')"}, "stage": "finished", "steps": [{"statusDetails": {}, "stage": "running", "steps": [{"status": "failed", "statusDetails": {"message": "Fixture \"browser\" timeout of 0ms exceeded during setup.", "trace": "Fixture \"browser\" timeout of 0ms exceeded during setup."}, "stage": "finished", "steps": [{"status": "failed", "statusDetails": {"message": "Fixture \"browser\" timeout of 0ms exceeded during setup.", "trace": "Fixture \"browser\" timeout of 0ms exceeded during setup."}, "stage": "finished", "steps": [{"status": "failed", "statusDetails": {"message": "Fixture \"browser\" timeout of 0ms exceeded during setup.", "trace": "Fixture \"browser\" timeout of 0ms exceeded during setup."}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "name": "Launch browser", "start": 1761576226333, "uuid": "43eaa441-2bc5-405e-8ee2-209fb569d9a9", "stop": 1761576265617}], "attachments": [], "parameters": [], "name": "Fixture \"browser\"", "start": 1761576226327, "uuid": "6713bca6-fb70-4c72-8a53-15858e12e6bb", "stop": 1761576265628}], "attachments": [], "parameters": [], "name": "beforeEach hook", "start": 1761576226311, "uuid": "2cef6d39-c5df-4e17-8b66-a7cedb6db8a0", "stop": 1761576265613}], "attachments": [], "parameters": [], "name": "Before Hooks", "start": 1761576226310, "uuid": "0f00600f-1305-4bf1-9a85-221200ea4c77"}, {"statusDetails": {}, "stage": "finished", "steps": [], "attachments": [{"name": "trace", "source": "63a022d5-f70d-47dd-aef0-83e1bc2b0ba8-attachment.zip", "type": "application/vnd.allure.playwright-trace"}], "parameters": [], "start": 1761576265639, "name": "trace", "stop": 1761576265639}, {"status": "failed", "statusDetails": {"message": "TypeError: Cannot read properties of null (reading 'newContext')", "trace": "TypeError: Cannot read properties of null (reading 'newContext')"}, "stage": "finished", "steps": [{"status": "failed", "statusDetails": {"message": "TypeError: Cannot read properties of null (reading 'newContext')", "trace": "TypeError: Cannot read properties of null (reading 'newContext')"}, "stage": "finished", "steps": [{"status": "failed", "statusDetails": {"message": "TypeError: Cannot read properties of null (reading 'newContext')", "trace": "TypeError: Cannot read properties of null (reading 'newContext')"}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "name": "Fixture \"context\"", "start": 1761576265614, "uuid": "aacad01d-b7e1-40bd-b28d-aa0a1f38096f", "stop": 1761576265616}], "attachments": [], "parameters": [], "name": "after<PERSON>ach hook", "start": 1761576265613, "uuid": "89c50183-7ece-4c52-8dda-99297ea3adf8", "stop": 1761576265616}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "name": "Fixture \"context\"", "start": 1761576265617, "uuid": "3ce2c8d1-17af-41ce-8c4c-05e7238e7ad5", "stop": 1761576265617}], "attachments": [], "parameters": [], "name": "After Hooks", "start": 1761576265612, "uuid": "59d8841d-d604-4fca-bdce-1c7f556577ff", "stop": 1761576265626}], "attachments": [], "parameters": [{"name": "Project", "value": "chromium"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "eap-application.test.ts"}, {"name": "titlePath", "value": " > chromium > eap-application.test.ts > EAP Application Tests"}, {"name": "tag", "value": "smoke"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-46112-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "subSuite", "value": "EAP Application Tests"}], "links": [], "start": 1761576226306, "testCaseId": "e9d8af61ba8124fb85c5e869b230719a", "fullName": "eap-application.test.ts:33:7", "titlePath": ["eap-application.test.ts", "EAP Application Tests"], "stop": 1761576226314}