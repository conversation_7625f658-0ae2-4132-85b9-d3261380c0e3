import winston from 'winston';
import path from 'path';
import fs from 'fs';

export class Logger {
  private static instance: Logger;
  private logger: winston.Logger;

  private constructor() {
    // Ensure logs directory exists
    const logsDir = path.join(process.cwd(), 'logs');
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
    }

    // Custom format for log messages
    const customFormat = winston.format.combine(
      winston.format.timestamp({
        format: 'YYYY-MM-DD HH:mm:ss.SSS'
      }),
      winston.format.errors({ stack: true }),
      winston.format.printf(({ level, message, timestamp, stack }) => {
        if (stack) {
          return `[${timestamp}] ${level.toUpperCase()}: ${message}\n${stack}`;
        }
        return `[${timestamp}] ${level.toUpperCase()}: ${message}`;
      })
    );

    // Create winston logger instance
    this.logger = winston.createLogger({
      level: process.env.LOG_LEVEL || 'info',
      format: customFormat,
      transports: [
        // Console transport with colors
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.colorize(),
            customFormat
          )
        }),
        // File transport for all logs
        new winston.transports.File({
          filename: path.join(logsDir, 'test.log'),
          maxsize: 10 * 1024 * 1024, // 10MB
          maxFiles: 5,
          tailable: true
        }),
        // Separate file for errors
        new winston.transports.File({
          filename: path.join(logsDir, 'error.log'),
          level: 'error',
          maxsize: 5 * 1024 * 1024, // 5MB
          maxFiles: 3,
          tailable: true
        })
      ],
      exitOnError: false
    });
  }

  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  public info(message: string, meta?: any): void {
    this.logger.info(message, meta);
  }

  public warn(message: string, meta?: any): void {
    this.logger.warn(message, meta);
  }

  public error(message: string, error?: Error | any): void {
    if (error instanceof Error) {
      this.logger.error(message, { stack: error.stack, message: error.message });
    } else {
      this.logger.error(message, error);
    }
  }

  public debug(message: string, meta?: any): void {
    this.logger.debug(message, meta);
  }

  public verbose(message: string, meta?: any): void {
    this.logger.verbose(message, meta);
  }

  public testStart(testName: string): void {
    this.info(`🧪 TEST STARTED: ${testName}`);
  }

  public testEnd(testName: string, status: 'PASSED' | 'FAILED' | 'SKIPPED'): void {
    const emoji = status === 'PASSED' ? '✅' : status === 'FAILED' ? '❌' : '⏭️';
    this.info(`${emoji} TEST ${status}: ${testName}`);
  }

  public step(stepDescription: string): void {
    this.info(`📝 STEP: ${stepDescription}`);
  }

  public action(actionDescription: string): void {
    this.info(`🎯 ACTION: ${actionDescription}`);
  }

  public assertion(assertionDescription: string): void {
    this.info(`🔍 ASSERTION: ${assertionDescription}`);
  }

  public screenshot(screenshotPath: string): void {
    this.info(`📸 SCREENSHOT: ${screenshotPath}`);
  }

  public video(videoPath: string): void {
    this.info(`🎥 VIDEO: ${videoPath}`);
  }

  public trace(tracePath: string): void {
    this.info(`🔍 TRACE: ${tracePath}`);
  }
}
