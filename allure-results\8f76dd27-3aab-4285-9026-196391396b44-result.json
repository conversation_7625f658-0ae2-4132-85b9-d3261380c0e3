{"uuid": "8f76dd27-3aab-4285-9026-196391396b44", "name": "should display footer information", "historyId": "3742aab49d1a83d263641b8e6a4ecc9d:2d9eadd8e5698e677c4123f9b6b8cac6", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Microsoft Edge"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "home.test.ts"}, {"name": "titlePath", "value": " > Microsoft Edge > home.test.ts > RASP Home Page Tests - Basic"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-36364-worker-0"}, {"name": "parentSuite", "value": "Microsoft Edge"}, {"name": "subSuite", "value": "RASP Home Page Tests - Basic"}], "links": [], "start": 1761577068566, "testCaseId": "3742aab49d1a83d263641b8e6a4ecc9d", "fullName": "home.test.ts:127:7", "titlePath": ["home.test.ts", "RASP Home Page Tests - Basic"], "stop": 1761577068566}