import { Page, Locator, expect } from '@playwright/test';
import { Logger } from './logger';
import path from 'path';
import fs from 'fs';

export class TestHelpers {
  private logger: Logger;

  constructor() {
    this.logger = Logger.getInstance();
  }

  /**
   * Wait for element to be visible and enabled
   */
  async waitForElement(page: Page, selector: string, timeout: number = 30000): Promise<Locator> {
    this.logger.action(`Waiting for element: ${selector}`);
    const element = page.locator(selector);
    await element.waitFor({ state: 'visible', timeout });
    return element;
  }

  /**
   * Safe click with retry mechanism
   */
  async safeClick(page: Page, selector: string, retries: number = 3): Promise<void> {
    this.logger.action(`Clicking element: ${selector}`);
    
    for (let i = 0; i < retries; i++) {
      try {
        const element = await this.waitForElement(page, selector);
        await element.click();
        this.logger.info(`Successfully clicked: ${selector}`);
        return;
      } catch (error) {
        this.logger.warn(`Click attempt ${i + 1} failed for ${selector}: ${error}`);
        if (i === retries - 1) {
          throw error;
        }
        await page.waitForTimeout(1000);
      }
    }
  }

  /**
   * Safe type with clear and retry mechanism
   */
  async safeType(page: Page, selector: string, text: string, retries: number = 3): Promise<void> {
    this.logger.action(`Typing text into element: ${selector}`);
    
    for (let i = 0; i < retries; i++) {
      try {
        const element = await this.waitForElement(page, selector);
        await element.clear();
        await element.fill(text);
        this.logger.info(`Successfully typed text into: ${selector}`);
        return;
      } catch (error) {
        this.logger.warn(`Type attempt ${i + 1} failed for ${selector}: ${error}`);
        if (i === retries - 1) {
          throw error;
        }
        await page.waitForTimeout(1000);
      }
    }
  }

  /**
   * Wait for page to load completely
   */
  async waitForPageLoad(page: Page, timeout: number = 30000): Promise<void> {
    this.logger.action('Waiting for page to load completely');
    await page.waitForLoadState('networkidle', { timeout });
    this.logger.info('Page loaded successfully');
  }

  /**
   * Take screenshot with timestamp
   */
  async takeScreenshot(page: Page, name: string): Promise<string> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const screenshotDir = path.join(process.cwd(), 'screenshots');
    
    if (!fs.existsSync(screenshotDir)) {
      fs.mkdirSync(screenshotDir, { recursive: true });
    }
    
    const screenshotPath = path.join(screenshotDir, `${name}-${timestamp}.png`);
    await page.screenshot({ path: screenshotPath, fullPage: true });
    
    this.logger.screenshot(screenshotPath);
    return screenshotPath;
  }

  /**
   * Scroll element into view
   */
  async scrollIntoView(page: Page, selector: string): Promise<void> {
    this.logger.action(`Scrolling element into view: ${selector}`);
    const element = page.locator(selector);
    await element.scrollIntoViewIfNeeded();
  }

  /**
   * Wait for text to be visible
   */
  async waitForText(page: Page, text: string, timeout: number = 30000): Promise<void> {
    this.logger.action(`Waiting for text: ${text}`);
    await page.waitForSelector(`text=${text}`, { timeout });
    this.logger.info(`Text found: ${text}`);
  }

  /**
   * Select dropdown option by text
   */
  async selectDropdownByText(page: Page, selector: string, optionText: string): Promise<void> {
    this.logger.action(`Selecting dropdown option: ${optionText} in ${selector}`);
    const dropdown = await this.waitForElement(page, selector);
    await dropdown.selectOption({ label: optionText });
    this.logger.info(`Selected option: ${optionText}`);
  }

  /**
   * Check if element exists
   */
  async elementExists(page: Page, selector: string): Promise<boolean> {
    try {
      await page.locator(selector).waitFor({ state: 'attached', timeout: 5000 });
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get element text content
   */
  async getElementText(page: Page, selector: string): Promise<string> {
    this.logger.action(`Getting text from element: ${selector}`);
    const element = await this.waitForElement(page, selector);
    const text = await element.textContent() || '';
    this.logger.info(`Element text: ${text}`);
    return text.trim();
  }

  /**
   * Wait for URL to contain specific text
   */
  async waitForUrlContains(page: Page, urlPart: string, timeout: number = 30000): Promise<void> {
    this.logger.action(`Waiting for URL to contain: ${urlPart}`);
    await page.waitForURL(`**/*${urlPart}*`, { timeout });
    this.logger.info(`URL contains: ${urlPart}`);
  }

  /**
   * Handle alert dialogs
   */
  async handleAlert(page: Page, action: 'accept' | 'dismiss' = 'accept'): Promise<void> {
    this.logger.action(`Handling alert with action: ${action}`);
    
    page.on('dialog', async dialog => {
      this.logger.info(`Alert message: ${dialog.message()}`);
      if (action === 'accept') {
        await dialog.accept();
      } else {
        await dialog.dismiss();
      }
    });
  }

  /**
   * Generate random test data
   */
  generateTestData() {
    const timestamp = Date.now();
    return {
      email: `test.user.${timestamp}@example.com`,
      firstName: `TestFirst${timestamp}`,
      lastName: `TestLast${timestamp}`,
      phone: `416555${String(timestamp).slice(-4)}`,
      postalCode: 'M5V 3A8',
      sin: `123456${String(timestamp).slice(-3)}`,
    };
  }

  /**
   * Format date for form inputs
   */
  formatDate(date: Date, format: 'YYYY-MM-DD' | 'MM/DD/YYYY' | 'DD/MM/YYYY' = 'YYYY-MM-DD'): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    switch (format) {
      case 'MM/DD/YYYY':
        return `${month}/${day}/${year}`;
      case 'DD/MM/YYYY':
        return `${day}/${month}/${year}`;
      default:
        return `${year}-${month}-${day}`;
    }
  }
}
