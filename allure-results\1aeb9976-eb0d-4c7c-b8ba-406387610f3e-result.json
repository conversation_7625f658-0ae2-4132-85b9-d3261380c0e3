{"uuid": "1aeb9976-eb0d-4c7c-b8ba-406387610f3e", "name": "should fill personal information with valid data @smoke", "historyId": "d6be0fe5bd8f380ea72fde811175b871:84e28e814b821ed013329cc8dbc467e0", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "webkit"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "eap-application.test.ts"}, {"name": "titlePath", "value": " > webkit > eap-application.test.ts > EAP Application Tests"}, {"name": "tag", "value": "smoke"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-49836-worker-0"}, {"name": "parentSuite", "value": "webkit"}, {"name": "subSuite", "value": "EAP Application Tests"}], "links": [], "start": 1761576670041, "testCaseId": "d6be0fe5bd8f380ea72fde811175b871", "fullName": "eap-application.test.ts:50:7", "titlePath": ["eap-application.test.ts", "EAP Application Tests"], "stop": 1761576670041}