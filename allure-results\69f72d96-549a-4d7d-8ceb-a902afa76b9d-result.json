{"uuid": "69f72d96-549a-4d7d-8ceb-a902afa76b9d", "name": "should fill employment information with valid data @smoke", "historyId": "fddf3058ae0350df7ba330116bf154fd:4151609b82fd60da52fe302128be4a3c", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Google Chrome"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "esi-application.test.ts"}, {"name": "titlePath", "value": " > Google Chrome > esi-application.test.ts > ESI Application Tests"}, {"name": "tag", "value": "smoke"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-49836-worker-0"}, {"name": "parentSuite", "value": "Google Chrome"}, {"name": "subSuite", "value": "ESI Application Tests"}], "links": [], "start": 1761576670085, "testCaseId": "fddf3058ae0350df7ba330116bf154fd", "fullName": "esi-application.test.ts:74:7", "titlePath": ["esi-application.test.ts", "ESI Application Tests"], "stop": 1761576670085}