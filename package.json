{"name": "rasp-playwright-framework", "version": "1.0.0", "description": "Playwright TypeScript framework for RASP application testing (ESI and EAP programs)", "main": "index.js", "scripts": {"test": "playwright test", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug", "test:ui": "playwright test --ui", "test:report": "playwright show-report", "test:esi": "playwright test --grep=\"ESI\"", "test:eap": "playwright test --grep=\"EAP\"", "test:smoke": "playwright test --grep=\"@smoke\"", "test:regression": "playwright test --grep=\"@regression\"", "install:browsers": "playwright install", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "build": "tsc", "clean": "rimraf dist reports/html-report reports/test-results", "pretest": "npm run clean"}, "keywords": ["playwright", "typescript", "automation", "testing", "e2e", "rasp", "esi", "eap"], "author": "SDET Team", "license": "MIT", "devDependencies": {"@playwright/test": "^1.40.0", "@types/node": "^20.8.0", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "eslint": "^8.50.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-playwright": "^0.16.0", "prettier": "^3.0.3", "rimraf": "^5.0.5", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "dependencies": {"winston": "^3.10.0", "dotenv": "^16.3.1", "moment": "^2.29.4", "xlsx": "^0.18.5"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}