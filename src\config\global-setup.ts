import { FullConfig } from '@playwright/test';
import { Logger } from '../utils/logger';

async function globalSetup(config: FullConfig) {
  const logger = Logger.getInstance();
  
  logger.info('🚀 Starting Playwright Test Suite');
  logger.info(`Environment: ${process.env.TEST_ENV || 'QA'}`);
  logger.info(`Base URL: ${process.env.BASE_URL || 'https://qa4.employmentontario.labour.gov.on.ca'}`);
  logger.info(`Browser: ${process.env.BROWSER || 'chromium'}`);
  logger.info(`Workers: ${config.workers}`);
  logger.info(`Retries: ${config.retries}`);
  
  // Create necessary directories
  const fs = require('fs');
  const path = require('path');
  
  const directories = [
    'reports',
    'reports/html-report',
    'reports/test-results',
    'reports/allure-results',
    'logs',
    'screenshots',
    'videos',
    'traces'
  ];
  
  directories.forEach(dir => {
    const dirPath = path.join(process.cwd(), dir);
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
      logger.info(`Created directory: ${dir}`);
    }
  });
  
  // Log test configuration
  logger.info('Test Configuration:');
  logger.info(`- Headless: ${process.env.HEADLESS !== 'false'}`);
  logger.info(`- Screenshot on failure: ${process.env.SCREENSHOT_ON_FAILURE !== 'false'}`);
  logger.info(`- Video on failure: ${process.env.VIDEO_ON_FAILURE !== 'false'}`);
  logger.info(`- Trace on retry: ${process.env.TRACE_ON_RETRY !== 'false'}`);
  
  // Validate environment variables
  const requiredEnvVars = ['BASE_URL'];
  const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);
  
  if (missingEnvVars.length > 0) {
    logger.warn(`Missing environment variables: ${missingEnvVars.join(', ')}`);
    logger.warn('Using default values. Consider creating a .env file.');
  }
  
  logger.info('✅ Global setup completed successfully');
}

export default globalSetup;
