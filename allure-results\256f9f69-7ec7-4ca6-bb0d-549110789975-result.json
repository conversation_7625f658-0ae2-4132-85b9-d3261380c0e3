{"uuid": "256f9f69-7ec7-4ca6-bb0d-************", "name": "should fill employment information for employed applicant @smoke", "historyId": "3684773979b995ace91c6963ffaac888:f0494070a4792d94c64308aac1604abb", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Safari"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "eap-application.test.ts"}, {"name": "titlePath", "value": " > Mobile Safari > eap-application.test.ts > EAP Application Tests"}, {"name": "tag", "value": "smoke"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-49836-worker-0"}, {"name": "parentSuite", "value": "Mobile Safari"}, {"name": "subSuite", "value": "EAP Application Tests"}], "links": [], "start": 1761576670059, "testCaseId": "3684773979b995ace91c6963ffaac888", "fullName": "eap-application.test.ts:62:7", "titlePath": ["eap-application.test.ts", "EAP Application Tests"], "stop": 1761576670059}