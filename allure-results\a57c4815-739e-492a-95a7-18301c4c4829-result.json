{"uuid": "a57c4815-739e-492a-95a7-18301c4c4829", "name": "should handle browser back navigation @regression", "historyId": "2d16f64fb5e901f85d4ca02bdf65c012:2d9eadd8e5698e677c4123f9b6b8cac6", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Microsoft Edge"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "home.test.ts"}, {"name": "titlePath", "value": " > Microsoft Edge > home.test.ts > RASP Home Page Tests - Basic"}, {"name": "tag", "value": "regression"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-36364-worker-0"}, {"name": "parentSuite", "value": "Microsoft Edge"}, {"name": "subSuite", "value": "RASP Home Page Tests - Basic"}], "links": [], "start": 1761577068568, "testCaseId": "2d16f64fb5e901f85d4ca02bdf65c012", "fullName": "home.test.ts:152:7", "titlePath": ["home.test.ts", "RASP Home Page Tests - Basic"], "stop": 1761577068568}