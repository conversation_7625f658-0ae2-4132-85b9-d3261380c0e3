{"uuid": "63bc183f-98cc-4214-9eb1-688a0ef38abc", "name": "should display welcome message", "historyId": "636af9f23ac4d8bea4e6db7d0ec9cba1:b444eb0fbe6390c71e68b51dd25701fc", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "firefox"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "home.test.ts"}, {"name": "titlePath", "value": " > firefox > home.test.ts > RASP Home Page Tests - Basic"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-36364-worker-0"}, {"name": "parentSuite", "value": "firefox"}, {"name": "subSuite", "value": "RASP Home Page Tests - Basic"}], "links": [], "start": 1761577068523, "testCaseId": "636af9f23ac4d8bea4e6db7d0ec9cba1", "fullName": "home.test.ts:102:7", "titlePath": ["home.test.ts", "RASP Home Page Tests - Basic"], "stop": 1761577068523}