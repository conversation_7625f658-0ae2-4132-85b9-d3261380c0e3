{"uuid": "b659738d-1cda-4e33-8857-6276e0bfec3f", "name": "should handle page refresh correctly @regression", "historyId": "65ede488e88d5e46930bd195693a89fe:4151609b82fd60da52fe302128be4a3c", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Google Chrome"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "home.test.ts"}, {"name": "titlePath", "value": " > Google Chrome > home.test.ts > RASP Home Page Tests - Basic"}, {"name": "tag", "value": "regression"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-36364-worker-0"}, {"name": "parentSuite", "value": "Google Chrome"}, {"name": "subSuite", "value": "RASP Home Page Tests - Basic"}], "links": [], "start": 1761577068578, "testCaseId": "65ede488e88d5e46930bd195693a89fe", "fullName": "home.test.ts:135:7", "titlePath": ["home.test.ts", "RASP Home Page Tests - Basic"], "stop": 1761577068578}