{"uuid": "e151904e-a846-4088-a345-19348ef79423", "name": "should show available programs", "historyId": "433f024c28fa98fab5bbb2d1e7ff7e4b:96797789f4669ba5e9de53cebde9126b", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Chrome"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "home.test.ts"}, {"name": "titlePath", "value": " > Mobile Chrome > home.test.ts > RASP Home Page Tests - Basic"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-36364-worker-0"}, {"name": "parentSuite", "value": "Mobile Chrome"}, {"name": "subSuite", "value": "RASP Home Page Tests - Basic"}], "links": [], "start": 1761577068546, "testCaseId": "433f024c28fa98fab5bbb2d1e7ff7e4b", "fullName": "home.test.ts:114:7", "titlePath": ["home.test.ts", "RASP Home Page Tests - Basic"], "stop": 1761577068546}