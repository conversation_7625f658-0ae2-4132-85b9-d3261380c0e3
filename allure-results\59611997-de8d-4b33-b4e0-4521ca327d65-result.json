{"uuid": "59611997-de8d-4b33-b4e0-4521ca327d65", "name": "should maintain session across page navigation @regression", "historyId": "b0b0ba68ae5215d068f25ad2f71bb580:b444eb0fbe6390c71e68b51dd25701fc", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "firefox"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "home.test.ts"}, {"name": "titlePath", "value": " > firefox > home.test.ts > RASP Home Page Tests - Basic"}, {"name": "tag", "value": "regression"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-36364-worker-0"}, {"name": "parentSuite", "value": "firefox"}, {"name": "subSuite", "value": "RASP Home Page Tests - Basic"}], "links": [], "start": 1761577068528, "testCaseId": "b0b0ba68ae5215d068f25ad2f71bb580", "fullName": "home.test.ts:167:7", "titlePath": ["home.test.ts", "RASP Home Page Tests - Basic"], "stop": 1761577068528}