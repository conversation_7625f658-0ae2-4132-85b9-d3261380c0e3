{"uuid": "c31e3c94-b16c-4d9e-9fde-343e0c3f1d62", "name": "should fill personal information with valid data @smoke", "historyId": "d6be0fe5bd8f380ea72fde811175b871:f0494070a4792d94c64308aac1604abb", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Safari"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "eap-application.test.ts"}, {"name": "titlePath", "value": " > Mobile Safari > eap-application.test.ts > EAP Application Tests"}, {"name": "tag", "value": "smoke"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-49836-worker-0"}, {"name": "parentSuite", "value": "Mobile Safari"}, {"name": "subSuite", "value": "EAP Application Tests"}], "links": [], "start": 1761576670058, "testCaseId": "d6be0fe5bd8f380ea72fde811175b871", "fullName": "eap-application.test.ts:50:7", "titlePath": ["eap-application.test.ts", "EAP Application Tests"], "stop": 1761576670058}