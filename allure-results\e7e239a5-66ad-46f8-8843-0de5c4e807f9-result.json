{"uuid": "e7e239a5-66ad-46f8-8843-0de5c4e807f9", "name": "should fill employment information with valid data @smoke", "historyId": "fddf3058ae0350df7ba330116bf154fd:5bd835b0d6b1d4ada3b9f0db936e82c8", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "chromium"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "esi-application.test.ts"}, {"name": "titlePath", "value": " > chromium > esi-application.test.ts > ESI Application Tests"}, {"name": "tag", "value": "smoke"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-49836-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "subSuite", "value": "ESI Application Tests"}], "links": [], "start": 1761576670030, "testCaseId": "fddf3058ae0350df7ba330116bf154fd", "fullName": "esi-application.test.ts:74:7", "titlePath": ["esi-application.test.ts", "ESI Application Tests"], "stop": 1761576670030}