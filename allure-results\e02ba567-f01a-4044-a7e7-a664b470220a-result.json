{"uuid": "e02ba567-f01a-4044-a7e7-a664b470220a", "name": "should fill employment information for employed applicant @smoke", "historyId": "3684773979b995ace91c6963ffaac888:84e28e814b821ed013329cc8dbc467e0", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "webkit"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "eap-application.test.ts"}, {"name": "titlePath", "value": " > webkit > eap-application.test.ts > EAP Application Tests"}, {"name": "tag", "value": "smoke"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-49836-worker-0"}, {"name": "parentSuite", "value": "webkit"}, {"name": "subSuite", "value": "EAP Application Tests"}], "links": [], "start": 1761576670042, "testCaseId": "3684773979b995ace91c6963ffaac888", "fullName": "eap-application.test.ts:62:7", "titlePath": ["eap-application.test.ts", "EAP Application Tests"], "stop": 1761576670042}