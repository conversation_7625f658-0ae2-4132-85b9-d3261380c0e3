import { Page, expect } from '@playwright/test';
import { BasePage } from './base.page';
import { getESIUrl, getEAPUrl } from '../config/test-config';

export class HomePage extends BasePage {
  // Page selectors
  private readonly pageHeader = 'h1, .page-title, [data-testid="page-header"]';
  private readonly languageSelector = '[data-testid="language-selector"], .language-toggle, select[name="language"]';
  private readonly esiProgramLink = '[data-testid="esi-program"], a[href*="ESI"], .program-esi';
  private readonly eapProgramLink = '[data-testid="eap-program"], a[href*="EAP"], .program-eap';
  private readonly programSelector = 'select[name="programCode"], #programCode, .program-selector';
  private readonly continueButton = '[data-testid="continue"], button[type="submit"], .btn-continue';
  private readonly welcomeMessage = '.welcome-message, [data-testid="welcome"], .intro-text';
  private readonly navigationMenu = '.nav-menu, [data-testid="navigation"], .main-nav';
  private readonly footerLinks = '.footer-links, [data-testid="footer"], footer';

  constructor(page: Page) {
    super(page);
  }

  /**
   * Navigate to RASP home page
   */
  async navigateToHome(): Promise<void> {
    const baseUrl = process.env.BASE_URL || 'https://qa4.employmentontario.labour.gov.on.ca';
    await this.navigateTo(`${baseUrl}/faec/`);
  }

  /**
   * Navigate directly to ESI program
   */
  async navigateToESI(): Promise<void> {
    const esiUrl = getESIUrl();
    await this.navigateTo(esiUrl);
  }

  /**
   * Navigate directly to EAP program
   */
  async navigateToEAP(): Promise<void> {
    const eapUrl = getEAPUrl();
    await this.navigateTo(eapUrl);
  }

  /**
   * Select language (English/French)
   */
  async selectLanguage(language: 'en' | 'fr'): Promise<void> {
    this.logger.action(`Selecting language: ${language}`);
    
    if (await this.isElementVisible(this.languageSelector)) {
      const languageText = language === 'en' ? 'English' : 'Français';
      await this.selectDropdown(this.languageSelector, languageText);
    } else {
      // Try to find language links
      const languageLink = `a[href*="lang=${language}"], .lang-${language}`;
      if (await this.isElementVisible(languageLink)) {
        await this.clickElement(languageLink);
      }
    }
    
    await this.waitForPageLoad();
    this.logger.info(`Language selected: ${language}`);
  }

  /**
   * Select program (ESI or EAP)
   */
  async selectProgram(programCode: 'ESI' | 'EAP'): Promise<void> {
    this.logger.action(`Selecting program: ${programCode}`);
    
    // Try dropdown selector first
    if (await this.isElementVisible(this.programSelector)) {
      await this.selectDropdown(this.programSelector, programCode);
    } else {
      // Try direct program links
      const programLink = programCode === 'ESI' ? this.esiProgramLink : this.eapProgramLink;
      if (await this.isElementVisible(programLink)) {
        await this.clickElement(programLink);
      } else {
        // Fallback: navigate directly to program URL
        if (programCode === 'ESI') {
          await this.navigateToESI();
        } else {
          await this.navigateToEAP();
        }
        return;
      }
    }
    
    // Click continue if button is present
    if (await this.isElementVisible(this.continueButton)) {
      await this.clickElement(this.continueButton);
    }
    
    await this.waitForPageLoad();
    this.logger.info(`Program selected: ${programCode}`);
  }

  /**
   * Get welcome message text
   */
  async getWelcomeMessage(): Promise<string> {
    if (await this.isElementVisible(this.welcomeMessage)) {
      return await this.getElementText(this.welcomeMessage);
    }
    return '';
  }

  /**
   * Check if navigation menu is visible
   */
  async isNavigationVisible(): Promise<boolean> {
    return await this.isElementVisible(this.navigationMenu);
  }

  /**
   * Get page header text
   */
  async getPageHeader(): Promise<string> {
    if (await this.isElementVisible(this.pageHeader)) {
      return await this.getElementText(this.pageHeader);
    }
    return '';
  }

  /**
   * Verify home page is loaded
   */
  async verifyPageLoaded(): Promise<void> {
    this.logger.assertion('Verifying home page is loaded');
    
    // Wait for page to load
    await this.waitForPageLoad();
    
    // Check for key elements that indicate the page is loaded
    const pageLoaded = await this.isElementVisible(this.pageHeader) ||
                      await this.isElementVisible(this.welcomeMessage) ||
                      await this.isElementVisible(this.programSelector);
    
    expect(pageLoaded).toBeTruthy();
    this.logger.info('Home page loaded successfully');
  }

  /**
   * Verify correct program is selected
   */
  async verifyProgramSelected(expectedProgram: 'ESI' | 'EAP'): Promise<void> {
    this.logger.assertion(`Verifying program ${expectedProgram} is selected`);
    
    // Check URL contains program code
    const currentUrl = await this.getCurrentUrl();
    expect(currentUrl).toContain(expectedProgram);
    
    // Check page title or header contains program name
    const pageTitle = await this.getPageTitle();
    const pageHeader = await this.getPageHeader();
    
    const programInTitle = pageTitle.includes(expectedProgram) || pageHeader.includes(expectedProgram);
    expect(programInTitle).toBeTruthy();
    
    this.logger.info(`Program ${expectedProgram} verified successfully`);
  }

  /**
   * Verify language is set correctly
   */
  async verifyLanguage(expectedLanguage: 'en' | 'fr'): Promise<void> {
    this.logger.assertion(`Verifying language is set to: ${expectedLanguage}`);
    
    // Check URL contains language parameter
    const currentUrl = await this.getCurrentUrl();
    expect(currentUrl).toContain(`lang=${expectedLanguage}`);
    
    this.logger.info(`Language ${expectedLanguage} verified successfully`);
  }

  /**
   * Check if footer is visible
   */
  async isFooterVisible(): Promise<boolean> {
    return await this.isElementVisible(this.footerLinks);
  }

  /**
   * Get all available programs from dropdown
   */
  async getAvailablePrograms(): Promise<string[]> {
    if (await this.isElementVisible(this.programSelector)) {
      const dropdown = this.page.locator(this.programSelector);
      const options = await dropdown.locator('option').allTextContents();
      return options.filter(option => option.trim() !== '');
    }
    return [];
  }
}
