import { Page, Locator, expect } from '@playwright/test';
import { Logger } from '../utils/logger';
import { TestHelpers } from '../utils/test-helpers';

export abstract class BasePage {
  protected page: Page;
  protected logger: Logger;
  protected helpers: TestHelpers;

  // Common selectors
  protected readonly loadingSpinner = '[data-testid="loading"], .loading, .spinner';
  protected readonly errorMessage = '[data-testid="error"], .error-message, .alert-danger';
  protected readonly successMessage = '[data-testid="success"], .success-message, .alert-success';
  protected readonly warningMessage = '[data-testid="warning"], .warning-message, .alert-warning';

  constructor(page: Page) {
    this.page = page;
    this.logger = Logger.getInstance();
    this.helpers = new TestHelpers();
  }

  /**
   * Navigate to a specific URL
   */
  async navigateTo(url: string): Promise<void> {
    this.logger.action(`Navigating to: ${url}`);
    await this.page.goto(url);
    await this.waitForPageLoad();
  }

  /**
   * Wait for page to load completely
   */
  async waitForPageLoad(): Promise<void> {
    await this.helpers.waitForPageLoad(this.page);
  }

  /**
   * Wait for loading to complete
   */
  async waitForLoadingToComplete(): Promise<void> {
    try {
      await this.page.waitForSelector(this.loadingSpinner, { state: 'hidden', timeout: 30000 });
      this.logger.info('Loading completed');
    } catch {
      // Loading spinner might not be present, which is fine
      this.logger.debug('No loading spinner found or already hidden');
    }
  }

  /**
   * Get page title
   */
  async getPageTitle(): Promise<string> {
    const title = await this.page.title();
    this.logger.info(`Page title: ${title}`);
    return title;
  }

  /**
   * Get current URL
   */
  async getCurrentUrl(): Promise<string> {
    const url = this.page.url();
    this.logger.info(`Current URL: ${url}`);
    return url;
  }

  /**
   * Check if element is visible
   */
  async isElementVisible(selector: string): Promise<boolean> {
    try {
      await this.page.locator(selector).waitFor({ state: 'visible', timeout: 5000 });
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Check if element is enabled
   */
  async isElementEnabled(selector: string): Promise<boolean> {
    try {
      const element = this.page.locator(selector);
      return await element.isEnabled();
    } catch {
      return false;
    }
  }

  /**
   * Click element with retry
   */
  async clickElement(selector: string): Promise<void> {
    await this.helpers.safeClick(this.page, selector);
  }

  /**
   * Type text into element
   */
  async typeText(selector: string, text: string): Promise<void> {
    await this.helpers.safeType(this.page, selector, text);
  }

  /**
   * Select dropdown option
   */
  async selectDropdown(selector: string, optionText: string): Promise<void> {
    await this.helpers.selectDropdownByText(this.page, selector, optionText);
  }

  /**
   * Get element text
   */
  async getElementText(selector: string): Promise<string> {
    return await this.helpers.getElementText(this.page, selector);
  }

  /**
   * Take screenshot
   */
  async takeScreenshot(name: string): Promise<string> {
    return await this.helpers.takeScreenshot(this.page, name);
  }

  /**
   * Scroll element into view
   */
  async scrollToElement(selector: string): Promise<void> {
    await this.helpers.scrollIntoView(this.page, selector);
  }

  /**
   * Wait for text to appear
   */
  async waitForText(text: string): Promise<void> {
    await this.helpers.waitForText(this.page, text);
  }

  /**
   * Check for error messages
   */
  async hasErrorMessage(): Promise<boolean> {
    return await this.isElementVisible(this.errorMessage);
  }

  /**
   * Get error message text
   */
  async getErrorMessage(): Promise<string> {
    if (await this.hasErrorMessage()) {
      return await this.getElementText(this.errorMessage);
    }
    return '';
  }

  /**
   * Check for success messages
   */
  async hasSuccessMessage(): Promise<boolean> {
    return await this.isElementVisible(this.successMessage);
  }

  /**
   * Get success message text
   */
  async getSuccessMessage(): Promise<string> {
    if (await this.hasSuccessMessage()) {
      return await this.getElementText(this.successMessage);
    }
    return '';
  }

  /**
   * Wait for URL to contain specific text
   */
  async waitForUrlContains(urlPart: string): Promise<void> {
    await this.helpers.waitForUrlContains(this.page, urlPart);
  }

  /**
   * Refresh the page
   */
  async refreshPage(): Promise<void> {
    this.logger.action('Refreshing page');
    await this.page.reload();
    await this.waitForPageLoad();
  }

  /**
   * Go back in browser history
   */
  async goBack(): Promise<void> {
    this.logger.action('Going back in browser history');
    await this.page.goBack();
    await this.waitForPageLoad();
  }

  /**
   * Verify page is loaded by checking for specific element
   */
  abstract async verifyPageLoaded(): Promise<void>;
}
