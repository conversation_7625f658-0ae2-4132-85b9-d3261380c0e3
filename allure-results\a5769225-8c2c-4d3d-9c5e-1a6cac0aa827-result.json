{"uuid": "a5769225-8c2c-4d3d-9c5e-1a6cac0aa827", "name": "should fill employment information with valid data @smoke", "historyId": "fddf3058ae0350df7ba330116bf154fd:96797789f4669ba5e9de53cebde9126b", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Chrome"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "esi-application.test.ts"}, {"name": "titlePath", "value": " > Mobile Chrome > esi-application.test.ts > ESI Application Tests"}, {"name": "tag", "value": "smoke"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-49836-worker-0"}, {"name": "parentSuite", "value": "Mobile Chrome"}, {"name": "subSuite", "value": "ESI Application Tests"}], "links": [], "start": 1761576670052, "testCaseId": "fddf3058ae0350df7ba330116bf154fd", "fullName": "esi-application.test.ts:74:7", "titlePath": ["esi-application.test.ts", "ESI Application Tests"], "stop": 1761576670052}