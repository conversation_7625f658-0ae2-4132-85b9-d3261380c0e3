{"uuid": "5ae1eed8-7eab-4cae-9f01-6bea93ec6ef1", "name": "should maintain session across page navigation @regression", "historyId": "b0b0ba68ae5215d068f25ad2f71bb580:96797789f4669ba5e9de53cebde9126b", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Chrome"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "home.test.ts"}, {"name": "titlePath", "value": " > Mobile Chrome > home.test.ts > RASP Home Page Tests - Basic"}, {"name": "tag", "value": "regression"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-36364-worker-0"}, {"name": "parentSuite", "value": "Mobile Chrome"}, {"name": "subSuite", "value": "RASP Home Page Tests - Basic"}], "links": [], "start": 1761577068551, "testCaseId": "b0b0ba68ae5215d068f25ad2f71bb580", "fullName": "home.test.ts:167:7", "titlePath": ["home.test.ts", "RASP Home Page Tests - Basic"], "stop": 1761577068551}