{"uuid": "a4d71c5c-ba5f-4885-b33e-2407246581b6", "name": "should fill personal information with valid data @smoke", "historyId": "0f23b63cfdba08a282a7e3981068f926:96797789f4669ba5e9de53cebde9126b", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Chrome"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "esi-application.test.ts"}, {"name": "titlePath", "value": " > Mobile Chrome > esi-application.test.ts > ESI Application Tests"}, {"name": "tag", "value": "smoke"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-49836-worker-0"}, {"name": "parentSuite", "value": "Mobile Chrome"}, {"name": "subSuite", "value": "ESI Application Tests"}], "links": [], "start": 1761576670051, "testCaseId": "0f23b63cfdba08a282a7e3981068f926", "fullName": "esi-application.test.ts:50:7", "titlePath": ["esi-application.test.ts", "ESI Application Tests"], "stop": 1761576670051}