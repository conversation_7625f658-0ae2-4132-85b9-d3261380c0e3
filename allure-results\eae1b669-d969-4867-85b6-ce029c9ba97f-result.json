{"uuid": "eae1b669-d969-4867-85b6-ce029c9ba97f", "name": "should allow language selection @smoke", "historyId": "9a3d66fde22d493c185b4ef9785ee99c:4151609b82fd60da52fe302128be4a3c", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Google Chrome"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "home.test.ts"}, {"name": "titlePath", "value": " > Google Chrome > home.test.ts > RASP Home Page Tests - Basic"}, {"name": "tag", "value": "smoke"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-49836-worker-0"}, {"name": "parentSuite", "value": "Google Chrome"}, {"name": "subSuite", "value": "RASP Home Page Tests - Basic"}], "links": [], "start": 1761576670087, "testCaseId": "9a3d66fde22d493c185b4ef9785ee99c", "fullName": "home.test.ts:42:7", "titlePath": ["home.test.ts", "RASP Home Page Tests - Basic"], "stop": 1761576670087}