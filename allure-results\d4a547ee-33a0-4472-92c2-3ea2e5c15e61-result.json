{"uuid": "d4a547ee-33a0-4472-92c2-3ea2e5c15e61", "name": "should display footer information", "historyId": "3742aab49d1a83d263641b8e6a4ecc9d:4151609b82fd60da52fe302128be4a3c", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Google Chrome"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "home.test.ts"}, {"name": "titlePath", "value": " > Google Chrome > home.test.ts > RASP Home Page Tests - Basic"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-36364-worker-0"}, {"name": "parentSuite", "value": "Google Chrome"}, {"name": "subSuite", "value": "RASP Home Page Tests - Basic"}], "links": [], "start": 1761577068577, "testCaseId": "3742aab49d1a83d263641b8e6a4ecc9d", "fullName": "home.test.ts:127:7", "titlePath": ["home.test.ts", "RASP Home Page Tests - Basic"], "stop": 1761577068577}