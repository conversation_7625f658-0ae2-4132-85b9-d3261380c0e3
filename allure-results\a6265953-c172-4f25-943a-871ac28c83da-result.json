{"uuid": "a6265953-c172-4f25-943a-871ac28c83da", "name": "should handle browser back navigation @regression", "historyId": "2d16f64fb5e901f85d4ca02bdf65c012:f0494070a4792d94c64308aac1604abb", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Safari"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "home.test.ts"}, {"name": "titlePath", "value": " > Mobile Safari > home.test.ts > RASP Home Page Tests - Basic"}, {"name": "tag", "value": "regression"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-36364-worker-0"}, {"name": "parentSuite", "value": "Mobile Safari"}, {"name": "subSuite", "value": "RASP Home Page Tests - Basic"}], "links": [], "start": 1761577068559, "testCaseId": "2d16f64fb5e901f85d4ca02bdf65c012", "fullName": "home.test.ts:152:7", "titlePath": ["home.test.ts", "RASP Home Page Tests - Basic"], "stop": 1761577068559}