{"uuid": "31c82b8b-4168-4996-a038-99cf281e2e0c", "name": "should show available programs", "historyId": "433f024c28fa98fab5bbb2d1e7ff7e4b:4151609b82fd60da52fe302128be4a3c", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Google Chrome"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "home.test.ts"}, {"name": "titlePath", "value": " > Google Chrome > home.test.ts > RASP Home Page Tests - Basic"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-36364-worker-0"}, {"name": "parentSuite", "value": "Google Chrome"}, {"name": "subSuite", "value": "RASP Home Page Tests - Basic"}], "links": [], "start": 1761577068575, "testCaseId": "433f024c28fa98fab5bbb2d1e7ff7e4b", "fullName": "home.test.ts:114:7", "titlePath": ["home.test.ts", "RASP Home Page Tests - Basic"], "stop": 1761577068575}