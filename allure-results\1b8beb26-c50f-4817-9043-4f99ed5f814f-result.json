{"uuid": "1b8beb26-c50f-4817-9043-4f99ed5f814f", "name": "should handle page refresh correctly @regression", "historyId": "65ede488e88d5e46930bd195693a89fe:5bd835b0d6b1d4ada3b9f0db936e82c8", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "chromium"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "home.test.ts"}, {"name": "titlePath", "value": " > chromium > home.test.ts > RASP Home Page Tests - Basic"}, {"name": "tag", "value": "regression"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-36364-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "subSuite", "value": "RASP Home Page Tests - Basic"}], "links": [], "start": 1761577068516, "testCaseId": "65ede488e88d5e46930bd195693a89fe", "fullName": "home.test.ts:135:7", "titlePath": ["home.test.ts", "RASP Home Page Tests - Basic"], "stop": 1761577068516}