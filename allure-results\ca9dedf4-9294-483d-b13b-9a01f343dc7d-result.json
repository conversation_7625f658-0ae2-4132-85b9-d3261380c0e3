{"uuid": "ca9dedf4-9294-483d-b13b-9a01f343dc7d", "name": "should allow language selection @smoke", "historyId": "9a3d66fde22d493c185b4ef9785ee99c:96797789f4669ba5e9de53cebde9126b", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Chrome"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "home.test.ts"}, {"name": "titlePath", "value": " > Mobile Chrome > home.test.ts > RASP Home Page Tests - Basic"}, {"name": "tag", "value": "smoke"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-49836-worker-0"}, {"name": "parentSuite", "value": "Mobile Chrome"}, {"name": "subSuite", "value": "RASP Home Page Tests - Basic"}], "links": [], "start": 1761576670054, "testCaseId": "9a3d66fde22d493c185b4ef9785ee99c", "fullName": "home.test.ts:42:7", "titlePath": ["home.test.ts", "RASP Home Page Tests - Basic"], "stop": 1761576670054}