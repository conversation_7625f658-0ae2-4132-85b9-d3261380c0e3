import { test, expect } from '@playwright/test';

// Simplified test without external dependencies for initial testing
test.describe('RASP Home Page Tests - Basic', () => {
  const baseUrl = 'https://qa4.employmentontario.labour.gov.on.ca/faec/?lang=en&programCode=ESI';

  test.beforeEach(async ({ page }) => {
    console.log('Navigating to RASP application...');
    await page.goto(baseUrl);
    await page.waitForLoadState('networkidle');
  });

  test.afterEach(async ({ page }, testInfo) => {
    if (testInfo.status === 'failed') {
      const screenshot = await page.screenshot({
        path: `screenshots/failed-${testInfo.title.replace(/\s+/g, '-')}.png`,
        fullPage: true
      });
      console.log(`Screenshot saved for failed test: ${testInfo.title}`);
    }
  });

  test('should load home page successfully @smoke', async ({ page }) => {
    console.log('Testing home page load...');

    // Verify page title
    const pageTitle = await page.title();
    expect(pageTitle).toBeTruthy();
    console.log(`Page title verified: ${pageTitle}`);

    // Verify page is loaded by checking for common elements
    const bodyElement = await page.locator('body').first();
    await expect(bodyElement).toBeVisible();
    console.log('Page body is visible');

    // Check if page contains expected content
    const pageContent = await page.textContent('body');
    expect(pageContent).toBeTruthy();
    console.log('Page content loaded successfully');
  });

  test('should allow language selection @smoke', async ({ page }) => {
    console.log('Testing language selection...');

    // Look for language selector (common patterns)
    const languageSelectors = [
      'select[name*="lang"]',
      'select[id*="lang"]',
      'a[href*="lang=fr"]',
      'button[data-lang]',
      '.language-selector'
    ];

    let languageElement = null;
    for (const selector of languageSelectors) {
      try {
        languageElement = await page.locator(selector).first();
        if (await languageElement.isVisible()) {
          console.log(`Found language selector: ${selector}`);
          break;
        }
      } catch (e) {
        // Continue to next selector
      }
    }

    if (languageElement && await languageElement.isVisible()) {
      console.log('Language selector found and is functional');
    } else {
      console.log('Language selector not found - may be handled differently');
    }
  });

  test('should navigate to ESI program @smoke', async ({ page }) => {
    console.log('Testing ESI program navigation...');

    // Current URL should contain ESI since we navigated to ESI URL
    const currentUrl = page.url();
    expect(currentUrl).toContain('ESI');
    console.log('Successfully verified ESI program URL');

    // Check for ESI-specific content
    const pageContent = await page.textContent('body');
    if (pageContent && pageContent.toLowerCase().includes('esi')) {
      console.log('ESI content found on page');
    }
  });

  test('should navigate to EAP program @smoke', async ({ page }) => {
    console.log('Testing EAP program navigation...');

    // Navigate to EAP URL
    const eapUrl = 'https://qa4.employmentontario.labour.gov.on.ca/faec/?lang=en&programCode=EAP';
    await page.goto(eapUrl);
    await page.waitForLoadState('networkidle');

    const currentUrl = page.url();
    expect(currentUrl).toContain('EAP');
    console.log('Successfully navigated to EAP program');
  });

  test('should display welcome message', async () => {
    logger.testStart('Welcome message display test');
    
    const welcomeMessage = await homePage.getWelcomeMessage();
    if (welcomeMessage) {
      expect(welcomeMessage.length).toBeGreaterThan(0);
      logger.assertion(`Welcome message displayed: ${welcomeMessage}`);
    } else {
      logger.info('No welcome message found on page');
    }
  });

  test('should show available programs', async () => {
    logger.testStart('Available programs test');
    
    const availablePrograms = await homePage.getAvailablePrograms();
    if (availablePrograms.length > 0) {
      expect(availablePrograms).toContain('ESI');
      expect(availablePrograms).toContain('EAP');
      logger.assertion(`Available programs: ${availablePrograms.join(', ')}`);
    } else {
      logger.info('No program dropdown found, using direct navigation');
    }
  });

  test('should display footer information', async () => {
    logger.testStart('Footer display test');
    
    const isFooterVisible = await homePage.isFooterVisible();
    expect(isFooterVisible).toBeTruthy();
    logger.assertion('Footer is visible');
  });

  test('should handle page refresh correctly @regression', async ({ page }) => {
    logger.testStart('Page refresh test');
    
    // Navigate to ESI first
    await homePage.selectProgram('ESI');
    await homePage.verifyProgramSelected('ESI');
    
    // Refresh the page
    await homePage.refreshPage();
    
    // Verify page still works after refresh
    await homePage.verifyPageLoaded();
    const currentUrl = await homePage.getCurrentUrl();
    expect(currentUrl).toContain('ESI');
    logger.assertion('Page refresh handled correctly');
  });

  test('should handle browser back navigation @regression', async ({ page }) => {
    logger.testStart('Browser back navigation test');
    
    // Navigate to ESI
    await homePage.selectProgram('ESI');
    await homePage.verifyProgramSelected('ESI');
    
    // Go back
    await homePage.goBack();
    
    // Verify we're back to home or previous page
    await homePage.verifyPageLoaded();
    logger.assertion('Browser back navigation handled correctly');
  });

  test('should maintain session across page navigation @regression', async () => {
    logger.testStart('Session maintenance test');
    
    // Select language
    await homePage.selectLanguage('en');
    
    // Navigate to ESI
    await homePage.selectProgram('ESI');
    
    // Verify language is still maintained
    const currentUrl = await homePage.getCurrentUrl();
    expect(currentUrl).toContain('lang=en');
    expect(currentUrl).toContain('ESI');
    logger.assertion('Session maintained across navigation');
  });

  test('should handle invalid program codes gracefully @negative', async ({ page }) => {
    logger.testStart('Invalid program code test');
    
    // Try to navigate to invalid program
    const invalidUrl = `${await homePage.getCurrentUrl()}?programCode=INVALID`;
    await page.goto(invalidUrl);
    
    // Should either redirect to valid page or show error
    const hasError = await homePage.hasErrorMessage();
    const currentUrl = await homePage.getCurrentUrl();
    
    if (hasError) {
      const errorMessage = await homePage.getErrorMessage();
      logger.assertion(`Error message displayed for invalid program: ${errorMessage}`);
    } else {
      // Should redirect to valid page
      expect(currentUrl).toMatch(/(ESI|EAP|faec)/);
      logger.assertion('Invalid program code redirected to valid page');
    }
  });
});
