import { test, expect } from '@playwright/test';

// Simplified test without external dependencies for initial testing
test.describe('RASP Home Page Tests - Basic', () => {
  const baseUrl = 'https://qa4.employmentontario.labour.gov.on.ca/faec/?lang=en&programCode=ESI';

  test.beforeEach(async ({ page }) => {
    console.log('Navigating to RASP application...');
    await page.goto(baseUrl);
    await page.waitForLoadState('networkidle');
  });

  test.afterEach(async ({ page }, testInfo) => {
    if (testInfo.status === 'failed') {
      const screenshot = await page.screenshot({
        path: `screenshots/failed-${testInfo.title.replace(/\s+/g, '-')}.png`,
        fullPage: true
      });
      console.log(`Screenshot saved for failed test: ${testInfo.title}`);
    }
  });

  test('should load home page successfully @smoke', async ({ page }) => {
    console.log('Testing home page load...');

    // Verify page title
    const pageTitle = await page.title();
    expect(pageTitle).toBeTruthy();
    console.log(`Page title verified: ${pageTitle}`);

    // Verify page is loaded by checking for common elements
    const bodyElement = await page.locator('body').first();
    await expect(bodyElement).toBeVisible();
    console.log('Page body is visible');

    // Check if page contains expected content
    const pageContent = await page.textContent('body');
    expect(pageContent).toBeTruthy();
    console.log('Page content loaded successfully');
  });

  test('should allow language selection @smoke', async ({ page }) => {
    console.log('Testing language selection...');

    // Look for language selector (common patterns)
    const languageSelectors = [
      'select[name*="lang"]',
      'select[id*="lang"]',
      'a[href*="lang=fr"]',
      'button[data-lang]',
      '.language-selector'
    ];

    let languageElement = null;
    for (const selector of languageSelectors) {
      try {
        languageElement = await page.locator(selector).first();
        if (await languageElement.isVisible()) {
          console.log(`Found language selector: ${selector}`);
          break;
        }
      } catch (e) {
        // Continue to next selector
      }
    }

    if (languageElement && await languageElement.isVisible()) {
      console.log('Language selector found and is functional');
    } else {
      console.log('Language selector not found - may be handled differently');
    }
  });

  test('should navigate to ESI program @smoke', async ({ page }) => {
    console.log('Testing ESI program navigation...');

    // Current URL should contain ESI since we navigated to ESI URL
    const currentUrl = page.url();
    expect(currentUrl).toContain('ESI');
    console.log('Successfully verified ESI program URL');

    // Check for ESI-specific content
    const pageContent = await page.textContent('body');
    if (pageContent && pageContent.toLowerCase().includes('esi')) {
      console.log('ESI content found on page');
    }
  });

  test('should navigate to EAP program @smoke', async ({ page }) => {
    console.log('Testing EAP program navigation...');

    // Navigate to EAP URL
    const eapUrl = 'https://qa4.employmentontario.labour.gov.on.ca/faec/?lang=en&programCode=EAP';
    await page.goto(eapUrl);
    await page.waitForLoadState('networkidle');

    const currentUrl = page.url();
    expect(currentUrl).toContain('EAP');
    console.log('Successfully navigated to EAP program');
  });

  test('should display welcome message', async ({ page }) => {
    console.log('Testing welcome message display...');

    // Look for common welcome message patterns
    const welcomeSelectors = [
      'h1', 'h2', '.welcome', '.header', '.title',
      '[data-testid*="welcome"]', '[class*="welcome"]'
    ];

    let welcomeFound = false;
    for (const selector of welcomeSelectors) {
      try {
        const element = await page.locator(selector).first();
        if (await element.isVisible()) {
          const text = await element.textContent();
          if (text && text.trim().length > 0) {
            console.log(`Welcome message found: ${text.substring(0, 100)}...`);
            welcomeFound = true;
            break;
          }
        }
      } catch (e) {
        // Continue to next selector
      }
    }

    if (!welcomeFound) {
      console.log('No specific welcome message found, but page loaded successfully');
    }
  });

  test('should show page content @regression', async ({ page }) => {
    console.log('Testing page content display...');

    // Check for main content areas
    const contentSelectors = ['main', '.content', '.container', '#content', 'form'];

    let contentFound = false;
    for (const selector of contentSelectors) {
      try {
        const element = await page.locator(selector).first();
        if (await element.isVisible()) {
          console.log(`Content area found: ${selector}`);
          contentFound = true;
          break;
        }
      } catch (e) {
        // Continue to next selector
      }
    }

    expect(contentFound).toBeTruthy();
  });

  test('should handle page refresh correctly @regression', async ({ page }) => {
    console.log('Testing page refresh...');

    // Get current URL
    const originalUrl = page.url();

    // Refresh the page
    await page.reload();
    await page.waitForLoadState('networkidle');

    // Verify page still works after refresh
    const newUrl = page.url();
    expect(newUrl).toBe(originalUrl);

    // Verify page content is still there
    const bodyElement = await page.locator('body').first();
    await expect(bodyElement).toBeVisible();
    console.log('Page refresh handled correctly');
  });

  test('should handle browser back navigation @regression', async ({ page }) => {
    console.log('Testing browser back navigation...');

    // Navigate to EAP
    const eapUrl = 'https://qa4.employmentontario.labour.gov.on.ca/faec/?lang=en&programCode=EAP';
    await page.goto(eapUrl);
    await page.waitForLoadState('networkidle');

    // Go back
    await page.goBack();
    await page.waitForLoadState('networkidle');

    // Verify we're back to ESI page
    const currentUrl = page.url();
    expect(currentUrl).toContain('ESI');
    console.log('Browser back navigation handled correctly');
  });

  test('should maintain session across page navigation @regression', async ({ page }) => {
    console.log('Testing session maintenance...');

    // Start with ESI URL
    const currentUrl = page.url();
    expect(currentUrl).toContain('lang=en');
    expect(currentUrl).toContain('ESI');

    // Navigate to EAP
    const eapUrl = 'https://qa4.employmentontario.labour.gov.on.ca/faec/?lang=en&programCode=EAP';
    await page.goto(eapUrl);
    await page.waitForLoadState('networkidle');

    // Verify language is still maintained
    const newUrl = page.url();
    expect(newUrl).toContain('lang=en');
    expect(newUrl).toContain('EAP');
    console.log('Session maintained across navigation');
  });

  test('should handle invalid program codes gracefully @negative', async ({ page }) => {
    console.log('Testing invalid program code handling...');

    // Try to navigate to invalid program
    const invalidUrl = 'https://qa4.employmentontario.labour.gov.on.ca/faec/?lang=en&programCode=INVALID';

    try {
      await page.goto(invalidUrl);
      await page.waitForLoadState('networkidle');

      const currentUrl = page.url();

      // Should either redirect to valid page or show error
      if (currentUrl.includes('INVALID')) {
        // Check for error message on page
        const pageContent = await page.textContent('body');
        const hasError = pageContent && (
          pageContent.toLowerCase().includes('error') ||
          pageContent.toLowerCase().includes('invalid') ||
          pageContent.toLowerCase().includes('not found')
        );

        if (hasError) {
          console.log('Error message displayed for invalid program code');
        } else {
          console.log('Invalid program code handled without explicit error');
        }
      } else {
        // Redirected to valid page
        expect(currentUrl).toMatch(/(ESI|EAP|faec)/);
        console.log('Invalid program code redirected to valid page');
      }
    } catch (error) {
      console.log('Invalid program code caused navigation error (expected behavior)');
    }
  });
});
