import { test, expect } from '@playwright/test';
import { HomePage } from '../pages/home.page';
import { Logger } from '../utils/logger';

test.describe('RASP Home Page Tests', () => {
  let homePage: HomePage;
  let logger: Logger;

  test.beforeEach(async ({ page }) => {
    logger = Logger.getInstance();
    homePage = new HomePage(page);
    
    logger.testStart('Home page setup');
    await homePage.navigateToHome();
    await homePage.verifyPageLoaded();
  });

  test.afterEach(async ({ page }, testInfo) => {
    const status = testInfo.status === 'passed' ? 'PASSED' : 'FAILED';
    logger.testEnd(testInfo.title, status);
    
    if (testInfo.status === 'failed') {
      await homePage.takeScreenshot(`failed-${testInfo.title.replace(/\s+/g, '-')}`);
    }
  });

  test('should load home page successfully @smoke', async () => {
    logger.testStart('Home page load verification');
    
    // Verify page title
    const pageTitle = await homePage.getPageTitle();
    expect(pageTitle).toBeTruthy();
    logger.assertion(`Page title verified: ${pageTitle}`);
    
    // Verify page header
    const pageHeader = await homePage.getPageHeader();
    expect(pageHeader).toBeTruthy();
    logger.assertion(`Page header verified: ${pageHeader}`);
    
    // Verify navigation is visible
    const isNavVisible = await homePage.isNavigationVisible();
    expect(isNavVisible).toBeTruthy();
    logger.assertion('Navigation menu is visible');
  });

  test('should allow language selection @smoke', async () => {
    logger.testStart('Language selection test');
    
    // Test English selection
    await homePage.selectLanguage('en');
    await homePage.verifyLanguage('en');
    logger.assertion('English language verified');
    
    // Test French selection
    await homePage.selectLanguage('fr');
    await homePage.verifyLanguage('fr');
    logger.assertion('French language verified');
    
    // Switch back to English
    await homePage.selectLanguage('en');
    await homePage.verifyLanguage('en');
  });

  test('should navigate to ESI program @smoke', async () => {
    logger.testStart('ESI program navigation test');
    
    await homePage.selectProgram('ESI');
    await homePage.verifyProgramSelected('ESI');
    
    const currentUrl = await homePage.getCurrentUrl();
    expect(currentUrl).toContain('ESI');
    logger.assertion('Successfully navigated to ESI program');
  });

  test('should navigate to EAP program @smoke', async () => {
    logger.testStart('EAP program navigation test');
    
    await homePage.selectProgram('EAP');
    await homePage.verifyProgramSelected('EAP');
    
    const currentUrl = await homePage.getCurrentUrl();
    expect(currentUrl).toContain('EAP');
    logger.assertion('Successfully navigated to EAP program');
  });

  test('should display welcome message', async () => {
    logger.testStart('Welcome message display test');
    
    const welcomeMessage = await homePage.getWelcomeMessage();
    if (welcomeMessage) {
      expect(welcomeMessage.length).toBeGreaterThan(0);
      logger.assertion(`Welcome message displayed: ${welcomeMessage}`);
    } else {
      logger.info('No welcome message found on page');
    }
  });

  test('should show available programs', async () => {
    logger.testStart('Available programs test');
    
    const availablePrograms = await homePage.getAvailablePrograms();
    if (availablePrograms.length > 0) {
      expect(availablePrograms).toContain('ESI');
      expect(availablePrograms).toContain('EAP');
      logger.assertion(`Available programs: ${availablePrograms.join(', ')}`);
    } else {
      logger.info('No program dropdown found, using direct navigation');
    }
  });

  test('should display footer information', async () => {
    logger.testStart('Footer display test');
    
    const isFooterVisible = await homePage.isFooterVisible();
    expect(isFooterVisible).toBeTruthy();
    logger.assertion('Footer is visible');
  });

  test('should handle page refresh correctly @regression', async ({ page }) => {
    logger.testStart('Page refresh test');
    
    // Navigate to ESI first
    await homePage.selectProgram('ESI');
    await homePage.verifyProgramSelected('ESI');
    
    // Refresh the page
    await homePage.refreshPage();
    
    // Verify page still works after refresh
    await homePage.verifyPageLoaded();
    const currentUrl = await homePage.getCurrentUrl();
    expect(currentUrl).toContain('ESI');
    logger.assertion('Page refresh handled correctly');
  });

  test('should handle browser back navigation @regression', async ({ page }) => {
    logger.testStart('Browser back navigation test');
    
    // Navigate to ESI
    await homePage.selectProgram('ESI');
    await homePage.verifyProgramSelected('ESI');
    
    // Go back
    await homePage.goBack();
    
    // Verify we're back to home or previous page
    await homePage.verifyPageLoaded();
    logger.assertion('Browser back navigation handled correctly');
  });

  test('should maintain session across page navigation @regression', async () => {
    logger.testStart('Session maintenance test');
    
    // Select language
    await homePage.selectLanguage('en');
    
    // Navigate to ESI
    await homePage.selectProgram('ESI');
    
    // Verify language is still maintained
    const currentUrl = await homePage.getCurrentUrl();
    expect(currentUrl).toContain('lang=en');
    expect(currentUrl).toContain('ESI');
    logger.assertion('Session maintained across navigation');
  });

  test('should handle invalid program codes gracefully @negative', async ({ page }) => {
    logger.testStart('Invalid program code test');
    
    // Try to navigate to invalid program
    const invalidUrl = `${await homePage.getCurrentUrl()}?programCode=INVALID`;
    await page.goto(invalidUrl);
    
    // Should either redirect to valid page or show error
    const hasError = await homePage.hasErrorMessage();
    const currentUrl = await homePage.getCurrentUrl();
    
    if (hasError) {
      const errorMessage = await homePage.getErrorMessage();
      logger.assertion(`Error message displayed for invalid program: ${errorMessage}`);
    } else {
      // Should redirect to valid page
      expect(currentUrl).toMatch(/(ESI|EAP|faec)/);
      logger.assertion('Invalid program code redirected to valid page');
    }
  });
});
