{"uuid": "017fa387-2c71-4622-83fe-1a491633f3cd", "name": "should show available programs", "historyId": "433f024c28fa98fab5bbb2d1e7ff7e4b:b444eb0fbe6390c71e68b51dd25701fc", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "firefox"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "home.test.ts"}, {"name": "titlePath", "value": " > firefox > home.test.ts > RASP Home Page Tests - Basic"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-36364-worker-0"}, {"name": "parentSuite", "value": "firefox"}, {"name": "subSuite", "value": "RASP Home Page Tests - Basic"}], "links": [], "start": 1761577068524, "testCaseId": "433f024c28fa98fab5bbb2d1e7ff7e4b", "fullName": "home.test.ts:114:7", "titlePath": ["home.test.ts", "RASP Home Page Tests - Basic"], "stop": 1761577068524}