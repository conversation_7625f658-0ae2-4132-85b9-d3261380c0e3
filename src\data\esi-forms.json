{"default": {"personalInfo": {"firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "dateOfBirth": "1990-01-15", "sin": "*********", "phone": "4165551234", "email": "<EMAIL>"}, "address": {"street": "123 Main Street", "city": "Toronto", "province": "Ontario", "postalCode": "M5V 3A8", "country": "Canada"}, "employment": {"status": "Unemployed", "lastEmployer": "ABC Company", "industry": "Technology", "reasonForLeaving": "Layoff", "lastWorkDate": "2023-12-01"}, "education": {"highestLevel": "Bachelor's Degree", "fieldOfStudy": "Computer Science", "institution": "University of Toronto", "graduationYear": "2012"}, "program": {"serviceType": "Employment Services", "goals": "Find full-time employment", "barriers": "<PERSON>k of recent experience", "preferredLanguage": "English"}}, "minimal": {"personalInfo": {"firstName": "<PERSON>", "lastName": "<PERSON>", "sin": "*********", "phone": "4165555678", "email": "<EMAIL>"}, "address": {"postalCode": "M4B 1B3"}, "employment": {"status": "Unemployed"}}, "complete": {"personalInfo": {"firstName": "<PERSON>", "lastName": "<PERSON>", "middleName": "<PERSON>", "dateOfBirth": "1985-06-20", "sin": "*********", "phone": "4165559876", "alternatePhone": "6475551234", "email": "micha<PERSON>.<EMAIL>", "preferredLanguage": "English", "gender": "Male", "maritalStatus": "Single"}, "address": {"street": "456 Oak Avenue", "unit": "Apt 2B", "city": "Toronto", "province": "Ontario", "postalCode": "M6K 2M2", "country": "Canada"}, "employment": {"status": "Unemployed", "lastEmployer": "XYZ Corporation", "jobTitle": "Software Developer", "industry": "Information Technology", "reasonForLeaving": "Contract ended", "lastWorkDate": "2023-11-30", "yearsOfExperience": "8", "salary": "75000"}, "education": {"highestLevel": "Master's Degree", "fieldOfStudy": "Software Engineering", "institution": "Ryerson University", "graduationYear": "2010", "additionalCertifications": "AWS Certified Developer"}, "program": {"serviceType": "Skills Development", "goals": "Upgrade technical skills and find employment", "barriers": "Technology skills gap", "preferredLanguage": "English", "accommodations": "None required", "transportation": "Public transit"}}}