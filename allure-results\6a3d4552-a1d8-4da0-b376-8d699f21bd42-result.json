{"uuid": "6a3d4552-a1d8-4da0-b376-8d699f21bd42", "name": "should handle browser back navigation @regression", "historyId": "2d16f64fb5e901f85d4ca02bdf65c012:96797789f4669ba5e9de53cebde9126b", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Chrome"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "home.test.ts"}, {"name": "titlePath", "value": " > Mobile Chrome > home.test.ts > RASP Home Page Tests - Basic"}, {"name": "tag", "value": "regression"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-36364-worker-0"}, {"name": "parentSuite", "value": "Mobile Chrome"}, {"name": "subSuite", "value": "RASP Home Page Tests - Basic"}], "links": [], "start": 1761577068550, "testCaseId": "2d16f64fb5e901f85d4ca02bdf65c012", "fullName": "home.test.ts:152:7", "titlePath": ["home.test.ts", "RASP Home Page Tests - Basic"], "stop": 1761577068550}