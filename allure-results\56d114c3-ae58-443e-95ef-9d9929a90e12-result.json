{"uuid": "56d114c3-ae58-443e-95ef-9d9929a90e12", "name": "should load home page successfully @smoke", "historyId": "c24ef460a995f3bae9a970650bec4a3d:5bd835b0d6b1d4ada3b9f0db936e82c8", "status": "failed", "statusDetails": {"message": "TypeError: Cannot read properties of null (reading 'newContext')", "trace": "TypeError: Cannot read properties of null (reading 'newContext')"}, "stage": "finished", "steps": [{"statusDetails": {}, "stage": "running", "steps": [{"status": "failed", "statusDetails": {"message": "Fixture \"browser\" timeout of 0ms exceeded during setup.", "trace": "Fixture \"browser\" timeout of 0ms exceeded during setup."}, "stage": "finished", "steps": [{"status": "failed", "statusDetails": {"message": "Fixture \"browser\" timeout of 0ms exceeded during setup.", "trace": "Fixture \"browser\" timeout of 0ms exceeded during setup."}, "stage": "finished", "steps": [{"status": "failed", "statusDetails": {"message": "Fixture \"browser\" timeout of 0ms exceeded during setup.", "trace": "Fixture \"browser\" timeout of 0ms exceeded during setup."}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "name": "Launch browser", "start": 1761576969595, "uuid": "9d1df41e-8173-4635-ba31-51cb1162775d", "stop": 1761577008028}], "attachments": [], "parameters": [], "name": "Fixture \"browser\"", "start": 1761576969592, "uuid": "ba1ff2e2-c1a2-43cd-b097-4dde4e9321f3", "stop": 1761577008036}], "attachments": [], "parameters": [], "name": "beforeEach hook", "start": 1761576969581, "uuid": "472e48c4-7df0-4398-90bc-598e6a67ac32", "stop": 1761577008026}], "attachments": [], "parameters": [], "name": "Before Hooks", "start": 1761576969581, "uuid": "188ae12a-cb3c-4306-a0ca-0c025f43a44d"}, {"status": "failed", "statusDetails": {"message": "TypeError: Cannot read properties of null (reading 'newContext')", "trace": "TypeError: Cannot read properties of null (reading 'newContext')"}, "stage": "finished", "steps": [{"status": "failed", "statusDetails": {"message": "TypeError: Cannot read properties of null (reading 'newContext')", "trace": "TypeError: Cannot read properties of null (reading 'newContext')"}, "stage": "finished", "steps": [{"status": "failed", "statusDetails": {"message": "TypeError: Cannot read properties of null (reading 'newContext')", "trace": "TypeError: Cannot read properties of null (reading 'newContext')"}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "name": "Fixture \"context\"", "start": 1761577008029, "uuid": "dd2309a1-f5cf-4f41-9de0-3b946a093e9e", "stop": 1761577008032}], "attachments": [], "parameters": [], "name": "after<PERSON>ach hook", "start": 1761577008027, "uuid": "c2f82509-dabe-4f79-8334-21e63039a87f", "stop": 1761577008032}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "name": "Fixture \"context\"", "start": 1761577008033, "uuid": "578ebeb5-a325-4cf7-b40f-442ceb9bc298", "stop": 1761577008033}], "attachments": [], "parameters": [], "name": "After Hooks", "start": 1761577008026, "uuid": "3ea137cd-2e17-4206-9ccd-e5df7b85d2f9", "stop": 1761577008043}], "attachments": [], "parameters": [{"name": "Project", "value": "chromium"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "home.test.ts"}, {"name": "titlePath", "value": " > chromium > home.test.ts > RASP Home Page Tests - Basic"}, {"name": "tag", "value": "smoke"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-36364-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "subSuite", "value": "RASP Home Page Tests - Basic"}], "links": [], "start": 1761576969584, "testCaseId": "c24ef460a995f3bae9a970650bec4a3d", "fullName": "home.test.ts:23:7", "titlePath": ["home.test.ts", "RASP Home Page Tests - Basic"], "stop": 1761576969590}