#!/bin/bash

# RASP Playwright Framework Setup Script
# This script sets up the complete testing framework

set -e  # Exit on any error

echo "🚀 Setting up RASP Playwright TypeScript Framework..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js is installed
check_nodejs() {
    print_status "Checking Node.js installation..."
    
    if command -v node >/dev/null 2>&1; then
        NODE_VERSION=$(node --version)
        print_success "Node.js is installed: $NODE_VERSION"
        
        # Check if version is >= 16
        NODE_MAJOR_VERSION=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
        if [ "$NODE_MAJOR_VERSION" -lt 16 ]; then
            print_error "Node.js version 16 or higher is required. Current version: $NODE_VERSION"
            exit 1
        fi
    else
        print_error "Node.js is not installed. Please install Node.js 16+ from https://nodejs.org/"
        exit 1
    fi
}

# Check if npm is installed
check_npm() {
    print_status "Checking npm installation..."
    
    if command -v npm >/dev/null 2>&1; then
        NPM_VERSION=$(npm --version)
        print_success "npm is installed: $NPM_VERSION"
    else
        print_error "npm is not installed. Please install npm."
        exit 1
    fi
}

# Install dependencies
install_dependencies() {
    print_status "Installing project dependencies..."
    
    if [ -f "package.json" ]; then
        npm install
        print_success "Dependencies installed successfully"
    else
        print_error "package.json not found. Make sure you're in the project root directory."
        exit 1
    fi
}

# Install Playwright browsers
install_browsers() {
    print_status "Installing Playwright browsers..."
    
    npx playwright install
    print_success "Playwright browsers installed successfully"
}

# Setup environment file
setup_environment() {
    print_status "Setting up environment configuration..."
    
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            cp .env.example .env
            print_success "Environment file created from .env.example"
            print_warning "Please edit .env file with your specific configuration"
        else
            print_warning ".env.example not found. Creating basic .env file..."
            cat > .env << EOF
# Test Environment
TEST_ENV=QA
BASE_URL=https://qa4.employmentontario.labour.gov.on.ca

# RASP Application URLs
ESI_URL=https://qa4.employmentontario.labour.gov.on.ca/faec/?lang=en&programCode=ESI
EAP_URL=https://qa4.employmentontario.labour.gov.on.ca/faec/?lang=en&programCode=EAP

# Browser Configuration
HEADLESS=false
BROWSER=chromium

# Test Execution
PARALLEL_WORKERS=1
RETRY_COUNT=2

# Reporting
SCREENSHOT_ON_FAILURE=true
VIDEO_ON_FAILURE=true
TRACE_ON_RETRY=true

# Logging
LOG_LEVEL=info
LOG_TO_FILE=true
LOG_FILE_PATH=logs/test.log
EOF
            print_success "Basic .env file created"
        fi
    else
        print_warning ".env file already exists. Skipping creation."
    fi
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    directories=("reports" "logs" "screenshots" "videos" "traces")
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            print_success "Created directory: $dir"
        fi
    done
}

# Run initial tests to verify setup
verify_setup() {
    print_status "Verifying framework setup..."
    
    # Build TypeScript
    print_status "Building TypeScript..."
    npm run build
    
    # Run linting
    print_status "Running code quality checks..."
    npm run lint
    
    # Run a simple test to verify everything works
    print_status "Running verification test..."
    if npm run test:smoke -- --reporter=line; then
        print_success "Framework setup verification completed successfully!"
    else
        print_warning "Some tests failed, but framework is set up. Check the logs for details."
    fi
}

# Display setup completion message
display_completion_message() {
    echo ""
    echo "🎉 RASP Playwright Framework Setup Complete!"
    echo ""
    echo "📁 Project Structure:"
    echo "   ├── src/tests/           # Test cases"
    echo "   ├── src/pages/           # Page Object Model"
    echo "   ├── src/utils/           # Utilities"
    echo "   ├── src/config/          # Configuration"
    echo "   ├── src/data/            # Test data"
    echo "   ├── reports/             # Test reports"
    echo "   └── docs/                # Documentation"
    echo ""
    echo "🚀 Quick Start Commands:"
    echo "   npm test                 # Run all tests"
    echo "   npm run test:headed      # Run with visible browser"
    echo "   npm run test:smoke       # Run smoke tests"
    echo "   npm run test:esi         # Run ESI tests"
    echo "   npm run test:eap         # Run EAP tests"
    echo "   npm run test:report      # View test reports"
    echo ""
    echo "📚 Documentation:"
    echo "   docs/getting-started.md          # Getting started guide"
    echo "   docs/test-writing-guidelines.md  # Test writing guidelines"
    echo "   docs/framework-architecture.md   # Framework architecture"
    echo ""
    echo "⚙️  Configuration:"
    echo "   .env                     # Environment variables"
    echo "   playwright.config.ts     # Playwright configuration"
    echo ""
    echo "🔧 Next Steps:"
    echo "   1. Edit .env file with your test environment details"
    echo "   2. Review the documentation in docs/ folder"
    echo "   3. Run 'npm run test:smoke' to verify everything works"
    echo "   4. Start writing your tests!"
    echo ""
}

# Main execution
main() {
    echo "Starting RASP Playwright Framework setup..."
    echo ""
    
    check_nodejs
    check_npm
    install_dependencies
    install_browsers
    setup_environment
    create_directories
    
    print_status "Setup completed. Running verification..."
    verify_setup
    
    display_completion_message
}

# Run main function
main "$@"
