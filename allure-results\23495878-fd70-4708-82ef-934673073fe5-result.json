{"uuid": "23495878-fd70-4708-82ef-934673073fe5", "name": "should fill address information with valid data @smoke", "historyId": "4c054fb698d8b75494c8f125516f9bbe:2d9eadd8e5698e677c4123f9b6b8cac6", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Microsoft Edge"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "esi-application.test.ts"}, {"name": "titlePath", "value": " > Microsoft Edge > esi-application.test.ts > ESI Application Tests"}, {"name": "tag", "value": "smoke"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-49836-worker-0"}, {"name": "parentSuite", "value": "Microsoft Edge"}, {"name": "subSuite", "value": "ESI Application Tests"}], "links": [], "start": 1761576670076, "testCaseId": "4c054fb698d8b75494c8f125516f9bbe", "fullName": "esi-application.test.ts:62:7", "titlePath": ["esi-application.test.ts", "ESI Application Tests"], "stop": 1761576670076}