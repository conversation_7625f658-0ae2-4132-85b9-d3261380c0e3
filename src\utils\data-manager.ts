import fs from 'fs';
import path from 'path';
import * as XLSX from 'xlsx';
import { Logger } from './logger';

export interface TestData {
  [key: string]: any;
}

export class DataManager {
  private logger: Logger;
  private dataDir: string;

  constructor() {
    this.logger = Logger.getInstance();
    this.dataDir = path.join(process.cwd(), 'src', 'data');
    this.ensureDataDirectory();
  }

  private ensureDataDirectory(): void {
    if (!fs.existsSync(this.dataDir)) {
      fs.mkdirSync(this.dataDir, { recursive: true });
      this.logger.info(`Created data directory: ${this.dataDir}`);
    }
  }

  /**
   * Load test data from JSON file
   */
  loadJsonData(fileName: string): TestData {
    const filePath = path.join(this.dataDir, `${fileName}.json`);
    
    try {
      if (!fs.existsSync(filePath)) {
        this.logger.warn(`JSON file not found: ${filePath}`);
        return {};
      }
      
      const rawData = fs.readFileSync(filePath, 'utf-8');
      const data = JSON.parse(rawData);
      this.logger.info(`Loaded JSON data from: ${fileName}.json`);
      return data;
    } catch (error) {
      this.logger.error(`Failed to load JSON data from ${fileName}.json`, error);
      return {};
    }
  }

  /**
   * Save test data to JSON file
   */
  saveJsonData(fileName: string, data: TestData): void {
    const filePath = path.join(this.dataDir, `${fileName}.json`);
    
    try {
      fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
      this.logger.info(`Saved JSON data to: ${fileName}.json`);
    } catch (error) {
      this.logger.error(`Failed to save JSON data to ${fileName}.json`, error);
    }
  }

  /**
   * Load test data from Excel file
   */
  loadExcelData(fileName: string, sheetName?: string): TestData[] {
    const filePath = path.join(this.dataDir, `${fileName}.xlsx`);
    
    try {
      if (!fs.existsSync(filePath)) {
        this.logger.warn(`Excel file not found: ${filePath}`);
        return [];
      }
      
      const workbook = XLSX.readFile(filePath);
      const sheet = sheetName || workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheet];
      const data = XLSX.utils.sheet_to_json(worksheet);
      
      this.logger.info(`Loaded Excel data from: ${fileName}.xlsx, sheet: ${sheet}`);
      return data as TestData[];
    } catch (error) {
      this.logger.error(`Failed to load Excel data from ${fileName}.xlsx`, error);
      return [];
    }
  }

  /**
   * Get test data by environment
   */
  getEnvironmentData(environment: string = 'qa'): TestData {
    return this.loadJsonData(`environment-${environment.toLowerCase()}`);
  }

  /**
   * Get user credentials for testing
   */
  getUserCredentials(userType: string = 'default'): TestData {
    const credentialsData = this.loadJsonData('credentials');
    return credentialsData[userType] || {};
  }

  /**
   * Get form data for specific program
   */
  getFormData(programCode: 'ESI' | 'EAP', formType: string = 'default'): TestData {
    const formData = this.loadJsonData(`${programCode.toLowerCase()}-forms`);
    return formData[formType] || {};
  }

  /**
   * Generate random test user data
   */
  generateRandomUserData(): TestData {
    const timestamp = Date.now();
    const randomNum = Math.floor(Math.random() * 1000);
    
    return {
      firstName: `TestUser${randomNum}`,
      lastName: `LastName${randomNum}`,
      email: `testuser${timestamp}@example.com`,
      phone: `416555${String(randomNum).padStart(4, '0')}`,
      dateOfBirth: this.generateRandomDate(18, 65),
      sin: this.generateRandomSIN(),
      address: {
        street: `${randomNum} Test Street`,
        city: 'Toronto',
        province: 'Ontario',
        postalCode: this.generateRandomPostalCode(),
        country: 'Canada'
      },
      employment: {
        status: 'Unemployed',
        lastEmployer: `Test Company ${randomNum}`,
        industry: 'Technology',
        experience: Math.floor(Math.random() * 20) + 1
      }
    };
  }

  /**
   * Generate random date within age range
   */
  private generateRandomDate(minAge: number, maxAge: number): string {
    const today = new Date();
    const minDate = new Date(today.getFullYear() - maxAge, today.getMonth(), today.getDate());
    const maxDate = new Date(today.getFullYear() - minAge, today.getMonth(), today.getDate());
    
    const randomTime = minDate.getTime() + Math.random() * (maxDate.getTime() - minDate.getTime());
    const randomDate = new Date(randomTime);
    
    return randomDate.toISOString().split('T')[0]; // YYYY-MM-DD format
  }

  /**
   * Generate random SIN (for testing purposes only)
   */
  private generateRandomSIN(): string {
    const firstDigit = Math.floor(Math.random() * 9) + 1; // 1-9
    const remainingDigits = Math.floor(Math.random() * *********).toString().padStart(8, '0');
    return `${firstDigit}${remainingDigits}`;
  }

  /**
   * Generate random Canadian postal code
   */
  private generateRandomPostalCode(): string {
    const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '0123456789';
    
    return `${letters[Math.floor(Math.random() * letters.length)]}${numbers[Math.floor(Math.random() * numbers.length)]}${letters[Math.floor(Math.random() * letters.length)]} ${numbers[Math.floor(Math.random() * numbers.length)]}${letters[Math.floor(Math.random() * letters.length)]}${numbers[Math.floor(Math.random() * numbers.length)]}`;
  }

  /**
   * Validate required fields in test data
   */
  validateTestData(data: TestData, requiredFields: string[]): boolean {
    const missingFields = requiredFields.filter(field => !data[field]);
    
    if (missingFields.length > 0) {
      this.logger.error(`Missing required fields: ${missingFields.join(', ')}`);
      return false;
    }
    
    return true;
  }
}
