{"uuid": "a3316169-ec02-4a4b-97da-f7c86e9340ce", "name": "should fill address information with valid data @smoke", "historyId": "4c054fb698d8b75494c8f125516f9bbe:96797789f4669ba5e9de53cebde9126b", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Chrome"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "esi-application.test.ts"}, {"name": "titlePath", "value": " > Mobile Chrome > esi-application.test.ts > ESI Application Tests"}, {"name": "tag", "value": "smoke"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-49836-worker-0"}, {"name": "parentSuite", "value": "Mobile Chrome"}, {"name": "subSuite", "value": "ESI Application Tests"}], "links": [], "start": 1761576670052, "testCaseId": "4c054fb698d8b75494c8f125516f9bbe", "fullName": "esi-application.test.ts:62:7", "titlePath": ["esi-application.test.ts", "ESI Application Tests"], "stop": 1761576670052}