{"uuid": "9e7c4802-07df-4bd2-a84c-afe197acf246", "name": "should load home page successfully @smoke", "historyId": "c24ef460a995f3bae9a970650bec4a3d:5bd835b0d6b1d4ada3b9f0db936e82c8", "status": "failed", "statusDetails": {"message": "TypeError: Cannot read properties of null (reading 'newContext')", "trace": "TypeError: Cannot read properties of null (reading 'newContext')"}, "stage": "finished", "steps": [{"statusDetails": {}, "stage": "running", "steps": [{"status": "failed", "statusDetails": {"message": "Fixture \"browser\" timeout of 0ms exceeded during setup.", "trace": "Fixture \"browser\" timeout of 0ms exceeded during setup."}, "stage": "finished", "steps": [{"status": "failed", "statusDetails": {"message": "Fixture \"browser\" timeout of 0ms exceeded during setup.", "trace": "Fixture \"browser\" timeout of 0ms exceeded during setup."}, "stage": "finished", "steps": [{"status": "failed", "statusDetails": {"message": "Fixture \"browser\" timeout of 0ms exceeded during setup.", "trace": "Fixture \"browser\" timeout of 0ms exceeded during setup."}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "name": "Launch browser", "start": 1761576064166, "uuid": "68f86a31-ddcf-4fbc-8bde-79cc39202433", "stop": 1761576132084}], "attachments": [], "parameters": [], "name": "Fixture \"browser\"", "start": 1761576064163, "uuid": "fb27db9a-040e-4ab0-aa00-7a6b53d36b2f", "stop": 1761576132091}], "attachments": [], "parameters": [], "name": "beforeEach hook", "start": 1761576064153, "uuid": "5cceaf77-**************-cab1087af986", "stop": 1761576132083}], "attachments": [], "parameters": [], "name": "Before Hooks", "start": 1761576064152, "uuid": "f068c755-7365-484a-a03b-63b105ac95b0"}, {"statusDetails": {}, "stage": "finished", "steps": [], "attachments": [{"name": "trace", "source": "f0f2b280-363b-4927-9d75-74bbf0d65c4f-attachment.zip", "type": "application/vnd.allure.playwright-trace"}], "parameters": [], "start": 1761576132122, "name": "trace", "stop": 1761576132123}, {"status": "failed", "statusDetails": {"message": "TypeError: Cannot read properties of null (reading 'newContext')", "trace": "TypeError: Cannot read properties of null (reading 'newContext')"}, "stage": "finished", "steps": [{"status": "failed", "statusDetails": {"message": "TypeError: Cannot read properties of null (reading 'newContext')", "trace": "TypeError: Cannot read properties of null (reading 'newContext')"}, "stage": "finished", "steps": [{"status": "failed", "statusDetails": {"message": "TypeError: Cannot read properties of null (reading 'newContext')", "trace": "TypeError: Cannot read properties of null (reading 'newContext')"}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "name": "Fixture \"context\"", "start": 1761576132084, "uuid": "2a71383d-ea8c-4eb5-a408-78118e40c75c", "stop": 1761576132087}], "attachments": [], "parameters": [], "name": "after<PERSON>ach hook", "start": 1761576132082, "uuid": "cac60f52-f42f-4801-85d4-50f947dec737", "stop": 1761576132087}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "name": "Fixture \"context\"", "start": 1761576132088, "uuid": "7e336ffc-f911-4dd1-b33c-b7058d389280", "stop": 1761576132088}], "attachments": [], "parameters": [], "name": "After Hooks", "start": 1761576132082, "uuid": "4ab5cbf7-0a1a-460e-8396-119f51b62c32", "stop": 1761576132098}], "attachments": [], "parameters": [{"name": "Project", "value": "chromium"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "home.test.ts"}, {"name": "titlePath", "value": " > chromium > home.test.ts > RASP Home Page Tests - Basic"}, {"name": "tag", "value": "smoke"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-46112-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "subSuite", "value": "RASP Home Page Tests - Basic"}], "links": [], "start": 1761576064165, "testCaseId": "c24ef460a995f3bae9a970650bec4a3d", "fullName": "home.test.ts:23:7", "titlePath": ["home.test.ts", "RASP Home Page Tests - Basic"], "stop": 1761576064172}