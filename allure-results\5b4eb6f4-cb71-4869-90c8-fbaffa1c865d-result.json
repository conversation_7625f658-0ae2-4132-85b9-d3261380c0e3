{"uuid": "5b4eb6f4-cb71-4869-90c8-fbaffa1c865d", "name": "should fill employment information for employed applicant @smoke", "historyId": "3684773979b995ace91c6963ffaac888:b444eb0fbe6390c71e68b51dd25701fc", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "firefox"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "eap-application.test.ts"}, {"name": "titlePath", "value": " > firefox > eap-application.test.ts > EAP Application Tests"}, {"name": "tag", "value": "smoke"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-49836-worker-0"}, {"name": "parentSuite", "value": "firefox"}, {"name": "subSuite", "value": "EAP Application Tests"}], "links": [], "start": 1761576670034, "testCaseId": "3684773979b995ace91c6963ffaac888", "fullName": "eap-application.test.ts:62:7", "titlePath": ["eap-application.test.ts", "EAP Application Tests"], "stop": 1761576670034}