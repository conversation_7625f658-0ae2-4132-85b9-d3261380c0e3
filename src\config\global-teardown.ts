import { FullConfig } from '@playwright/test';
import { Logger } from '../utils/logger';

async function globalTeardown(config: FullConfig) {
  const logger = Logger.getInstance();
  
  logger.info('🏁 Playwright Test Suite Completed');
  
  // Log test execution summary
  const endTime = new Date();
  logger.info(`Test execution completed at: ${endTime.toISOString()}`);
  
  // Clean up temporary files if needed
  const fs = require('fs');
  const path = require('path');
  
  try {
    // Archive old logs if they exist
    const logsDir = path.join(process.cwd(), 'logs');
    const archiveDir = path.join(logsDir, 'archive');
    
    if (fs.existsSync(logsDir) && fs.existsSync(path.join(logsDir, 'test.log'))) {
      if (!fs.existsSync(archiveDir)) {
        fs.mkdirSync(archiveDir, { recursive: true });
      }
      
      const timestamp = endTime.toISOString().replace(/[:.]/g, '-');
      const archiveFileName = `test-${timestamp}.log`;
      const archivePath = path.join(archiveDir, archiveFileName);
      
      // Copy current log to archive
      fs.copyFileSync(path.join(logsDir, 'test.log'), archivePath);
      logger.info(`Log archived to: ${archiveFileName}`);
    }
  } catch (error) {
    logger.warn(`Failed to archive logs: ${error}`);
  }
  
  // Log report locations
  logger.info('📊 Test Reports Generated:');
  logger.info('- HTML Report: reports/html-report/index.html');
  logger.info('- JSON Report: reports/test-results.json');
  logger.info('- JUnit Report: reports/test-results.xml');
  logger.info('- Allure Results: reports/allure-results/');
  
  logger.info('✅ Global teardown completed successfully');
}

export default globalTeardown;
