import { Page, expect } from '@playwright/test';
import { BasePage } from './base.page';

export class EAPApplicationPage extends BasePage {
  // Form section selectors
  private readonly personalInfoSection = '[data-testid="personal-info"], .personal-information, #personal-info';
  private readonly addressSection = '[data-testid="address"], .address-information, #address-info';
  private readonly employmentSection = '[data-testid="employment"], .employment-information, #employment-info';
  private readonly educationSection = '[data-testid="education"], .education-information, #education-info';
  private readonly programSection = '[data-testid="program"], .program-information, #program-info';

  // Personal Information fields
  private readonly firstNameField = 'input[name="firstName"], #firstName, [data-testid="first-name"]';
  private readonly lastNameField = 'input[name="lastName"], #lastName, [data-testid="last-name"]';
  private readonly dateOfBirthField = 'input[name="dateOfBirth"], #dateOfBirth, [data-testid="date-of-birth"]';
  private readonly sinField = 'input[name="sin"], #sin, [data-testid="sin"]';
  private readonly phoneField = 'input[name="phone"], #phone, [data-testid="phone"]';
  private readonly emailField = 'input[name="email"], #email, [data-testid="email"]';

  // Address fields
  private readonly streetField = 'input[name="street"], #street, [data-testid="street"]';
  private readonly cityField = 'input[name="city"], #city, [data-testid="city"]';
  private readonly provinceSelect = 'select[name="province"], #province, [data-testid="province"]';
  private readonly postalCodeField = 'input[name="postalCode"], #postalCode, [data-testid="postal-code"]';

  // Employment fields
  private readonly employmentStatusSelect = 'select[name="employmentStatus"], #employmentStatus, [data-testid="employment-status"]';
  private readonly currentEmployerField = 'input[name="currentEmployer"], #currentEmployer, [data-testid="current-employer"]';
  private readonly jobTitleField = 'input[name="jobTitle"], #jobTitle, [data-testid="job-title"]';
  private readonly industrySelect = 'select[name="industry"], #industry, [data-testid="industry"]';
  private readonly hoursPerWeekField = 'input[name="hoursPerWeek"], #hoursPerWeek, [data-testid="hours-per-week"]';
  private readonly hourlyWageField = 'input[name="hourlyWage"], #hourlyWage, [data-testid="hourly-wage"]';

  // Program specific fields
  private readonly serviceTypeSelect = 'select[name="serviceType"], #serviceType, [data-testid="service-type"]';
  private readonly goalsTextarea = 'textarea[name="goals"], #goals, [data-testid="goals"]';
  private readonly currentSkillsTextarea = 'textarea[name="currentSkills"], #currentSkills, [data-testid="current-skills"]';
  private readonly desiredSkillsTextarea = 'textarea[name="desiredSkills"], #desiredSkills, [data-testid="desired-skills"]';
  private readonly targetIndustrySelect = 'select[name="targetIndustry"], #targetIndustry, [data-testid="target-industry"]';

  // Apprenticeship specific fields
  private readonly interestedTradeSelect = 'select[name="interestedTrade"], #interestedTrade, [data-testid="interested-trade"]';
  private readonly previousExperienceTextarea = 'textarea[name="previousExperience"], #previousExperience, [data-testid="previous-experience"]';
  private readonly preferredLocationField = 'input[name="preferredLocation"], #preferredLocation, [data-testid="preferred-location"]';
  private readonly availabilityStartField = 'input[name="availabilityStart"], #availabilityStart, [data-testid="availability-start"]';

  // Navigation buttons
  private readonly nextButton = 'button[data-testid="next"], .btn-next, button:has-text("Next")';
  private readonly previousButton = 'button[data-testid="previous"], .btn-previous, button:has-text("Previous")';
  private readonly saveButton = 'button[data-testid="save"], .btn-save, button:has-text("Save")';
  private readonly submitButton = 'button[data-testid="submit"], .btn-submit, button:has-text("Submit")';

  constructor(page: Page) {
    super(page);
  }

  /**
   * Fill personal information section
   */
  async fillPersonalInformation(personalInfo: any): Promise<void> {
    this.logger.action('Filling personal information section for EAP');

    if (personalInfo.firstName) {
      await this.typeText(this.firstNameField, personalInfo.firstName);
    }

    if (personalInfo.lastName) {
      await this.typeText(this.lastNameField, personalInfo.lastName);
    }

    if (personalInfo.dateOfBirth) {
      await this.typeText(this.dateOfBirthField, personalInfo.dateOfBirth);
    }

    if (personalInfo.sin) {
      await this.typeText(this.sinField, personalInfo.sin);
    }

    if (personalInfo.phone) {
      await this.typeText(this.phoneField, personalInfo.phone);
    }

    if (personalInfo.email) {
      await this.typeText(this.emailField, personalInfo.email);
    }

    this.logger.info('Personal information filled successfully for EAP');
  }

  /**
   * Fill address information section
   */
  async fillAddressInformation(address: any): Promise<void> {
    this.logger.action('Filling address information section for EAP');

    if (address.street) {
      await this.typeText(this.streetField, address.street);
    }

    if (address.city) {
      await this.typeText(this.cityField, address.city);
    }

    if (address.province && await this.isElementVisible(this.provinceSelect)) {
      await this.selectDropdown(this.provinceSelect, address.province);
    }

    if (address.postalCode) {
      await this.typeText(this.postalCodeField, address.postalCode);
    }

    this.logger.info('Address information filled successfully for EAP');
  }

  /**
   * Fill employment information section
   */
  async fillEmploymentInformation(employment: any): Promise<void> {
    this.logger.action('Filling employment information section for EAP');

    if (employment.status) {
      await this.selectDropdown(this.employmentStatusSelect, employment.status);
    }

    if (employment.currentEmployer && await this.isElementVisible(this.currentEmployerField)) {
      await this.typeText(this.currentEmployerField, employment.currentEmployer);
    }

    if (employment.jobTitle && await this.isElementVisible(this.jobTitleField)) {
      await this.typeText(this.jobTitleField, employment.jobTitle);
    }

    if (employment.industry && await this.isElementVisible(this.industrySelect)) {
      await this.selectDropdown(this.industrySelect, employment.industry);
    }

    if (employment.hoursPerWeek && await this.isElementVisible(this.hoursPerWeekField)) {
      await this.typeText(this.hoursPerWeekField, employment.hoursPerWeek);
    }

    if (employment.hourlyWage && await this.isElementVisible(this.hourlyWageField)) {
      await this.typeText(this.hourlyWageField, employment.hourlyWage);
    }

    this.logger.info('Employment information filled successfully for EAP');
  }

  /**
   * Fill program information section
   */
  async fillProgramInformation(program: any): Promise<void> {
    this.logger.action('Filling program information section for EAP');

    if (program.serviceType && await this.isElementVisible(this.serviceTypeSelect)) {
      await this.selectDropdown(this.serviceTypeSelect, program.serviceType);
    }

    if (program.goals && await this.isElementVisible(this.goalsTextarea)) {
      await this.typeText(this.goalsTextarea, program.goals);
    }

    if (program.currentSkills && await this.isElementVisible(this.currentSkillsTextarea)) {
      await this.typeText(this.currentSkillsTextarea, program.currentSkills);
    }

    if (program.desiredSkills && await this.isElementVisible(this.desiredSkillsTextarea)) {
      await this.typeText(this.desiredSkillsTextarea, program.desiredSkills);
    }

    if (program.targetIndustry && await this.isElementVisible(this.targetIndustrySelect)) {
      await this.selectDropdown(this.targetIndustrySelect, program.targetIndustry);
    }

    this.logger.info('Program information filled successfully for EAP');
  }

  /**
   * Fill apprenticeship specific information
   */
  async fillApprenticeshipInformation(apprenticeship: any): Promise<void> {
    this.logger.action('Filling apprenticeship information for EAP');

    if (apprenticeship.interestedTrade && await this.isElementVisible(this.interestedTradeSelect)) {
      await this.selectDropdown(this.interestedTradeSelect, apprenticeship.interestedTrade);
    }

    if (apprenticeship.previousExperience && await this.isElementVisible(this.previousExperienceTextarea)) {
      await this.typeText(this.previousExperienceTextarea, apprenticeship.previousExperience);
    }

    if (apprenticeship.preferredLocation && await this.isElementVisible(this.preferredLocationField)) {
      await this.typeText(this.preferredLocationField, apprenticeship.preferredLocation);
    }

    if (apprenticeship.availabilityStart && await this.isElementVisible(this.availabilityStartField)) {
      await this.typeText(this.availabilityStartField, apprenticeship.availabilityStart);
    }

    this.logger.info('Apprenticeship information filled successfully for EAP');
  }

  /**
   * Navigate to next step
   */
  async clickNext(): Promise<void> {
    this.logger.action('Clicking Next button');
    await this.clickElement(this.nextButton);
    await this.waitForPageLoad();
  }

  /**
   * Navigate to previous step
   */
  async clickPrevious(): Promise<void> {
    this.logger.action('Clicking Previous button');
    await this.clickElement(this.previousButton);
    await this.waitForPageLoad();
  }

  /**
   * Save current progress
   */
  async clickSave(): Promise<void> {
    this.logger.action('Clicking Save button');
    await this.clickElement(this.saveButton);
    await this.waitForLoadingToComplete();
  }

  /**
   * Submit application
   */
  async clickSubmit(): Promise<void> {
    this.logger.action('Clicking Submit button');
    await this.clickElement(this.submitButton);
    await this.waitForLoadingToComplete();
  }

  /**
   * Verify page is loaded
   */
  async verifyPageLoaded(): Promise<void> {
    this.logger.assertion('Verifying EAP application page is loaded');
    
    await this.waitForPageLoad();
    
    const pageLoaded = await this.isElementVisible(this.personalInfoSection) ||
                      await this.isElementVisible(this.firstNameField) ||
                      await this.isElementVisible(this.serviceTypeSelect);
    
    expect(pageLoaded).toBeTruthy();
    this.logger.info('EAP application page loaded successfully');
  }

  /**
   * Fill complete EAP application
   */
  async fillCompleteApplication(applicationData: any): Promise<void> {
    this.logger.action('Filling complete EAP application');

    if (applicationData.personalInfo) {
      await this.fillPersonalInformation(applicationData.personalInfo);
    }

    if (applicationData.address) {
      await this.fillAddressInformation(applicationData.address);
    }

    if (applicationData.employment) {
      await this.fillEmploymentInformation(applicationData.employment);
    }

    if (applicationData.program) {
      await this.fillProgramInformation(applicationData.program);
    }

    // Fill apprenticeship info if it's an apprenticeship application
    if (applicationData.apprenticeship) {
      await this.fillApprenticeshipInformation(applicationData.apprenticeship);
    }

    this.logger.info('Complete EAP application filled successfully');
  }
}
