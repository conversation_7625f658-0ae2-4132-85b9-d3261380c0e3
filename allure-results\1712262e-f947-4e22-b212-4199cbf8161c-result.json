{"uuid": "1712262e-f947-4e22-b212-4199cbf8161c", "name": "should handle invalid program codes gracefully @negative", "historyId": "bf552e89616dfe6bfa6098d4b87bc6fc:b444eb0fbe6390c71e68b51dd25701fc", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "firefox"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "home.test.ts"}, {"name": "titlePath", "value": " > firefox > home.test.ts > RASP Home Page Tests - Basic"}, {"name": "tag", "value": "negative"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-36364-worker-0"}, {"name": "parentSuite", "value": "firefox"}, {"name": "subSuite", "value": "RASP Home Page Tests - Basic"}], "links": [], "start": 1761577068529, "testCaseId": "bf552e89616dfe6bfa6098d4b87bc6fc", "fullName": "home.test.ts:183:7", "titlePath": ["home.test.ts", "RASP Home Page Tests - Basic"], "stop": 1761577068529}