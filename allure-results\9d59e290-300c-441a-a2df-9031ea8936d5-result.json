{"uuid": "9d59e290-300c-441a-a2df-9031ea8936d5", "name": "should handle invalid program codes gracefully @negative", "historyId": "bf552e89616dfe6bfa6098d4b87bc6fc:2d9eadd8e5698e677c4123f9b6b8cac6", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Microsoft Edge"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "home.test.ts"}, {"name": "titlePath", "value": " > Microsoft Edge > home.test.ts > RASP Home Page Tests - Basic"}, {"name": "tag", "value": "negative"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-36364-worker-0"}, {"name": "parentSuite", "value": "Microsoft Edge"}, {"name": "subSuite", "value": "RASP Home Page Tests - Basic"}], "links": [], "start": 1761577068570, "testCaseId": "bf552e89616dfe6bfa6098d4b87bc6fc", "fullName": "home.test.ts:183:7", "titlePath": ["home.test.ts", "RASP Home Page Tests - Basic"], "stop": 1761577068570}