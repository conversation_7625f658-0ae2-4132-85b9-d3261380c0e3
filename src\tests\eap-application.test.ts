import { test, expect } from '@playwright/test';
import { HomePage } from '../pages/home.page';
import { EAPApplicationPage } from '../pages/eap-application.page';
import { DataManager } from '../utils/data-manager';
import { Logger } from '../utils/logger';

test.describe('EAP Application Tests', () => {
  let homePage: HomePage;
  let eapPage: EAPApplicationPage;
  let dataManager: DataManager;
  let logger: Logger;

  test.beforeEach(async ({ page }) => {
    logger = Logger.getInstance();
    homePage = new HomePage(page);
    eapPage = new EAPApplicationPage(page);
    dataManager = new DataManager();
    
    logger.testStart('EAP application setup');
    await homePage.navigateToEAP();
    await eapPage.verifyPageLoaded();
  });

  test.afterEach(async ({ page }, testInfo) => {
    const status = testInfo.status === 'passed' ? 'PASSED' : 'FAILED';
    logger.testEnd(testInfo.title, status);
    
    if (testInfo.status === 'failed') {
      await eapPage.takeScreenshot(`failed-eap-${testInfo.title.replace(/\s+/g, '-')}`);
    }
  });

  test('should load EAP application form @smoke', async () => {
    logger.testStart('EAP application form load test');
    
    // Verify page is loaded
    await eapPage.verifyPageLoaded();
    
    // Verify page title contains EAP
    const pageTitle = await eapPage.getPageTitle();
    expect(pageTitle.toLowerCase()).toContain('eap');
    logger.assertion(`EAP page title verified: ${pageTitle}`);
    
    // Verify URL contains EAP
    const currentUrl = await eapPage.getCurrentUrl();
    expect(currentUrl).toContain('EAP');
    logger.assertion('EAP URL verified');
  });

  test('should fill personal information with valid data @smoke', async () => {
    logger.testStart('EAP personal information test');
    
    const testData = dataManager.getFormData('EAP', 'default');
    await eapPage.fillPersonalInformation(testData.personalInfo);
    
    // Verify no error messages
    const hasError = await eapPage.hasErrorMessage();
    expect(hasError).toBeFalsy();
    logger.assertion('Personal information filled without errors');
  });

  test('should fill employment information for employed applicant @smoke', async () => {
    logger.testStart('EAP employment information test');
    
    const testData = dataManager.getFormData('EAP', 'default');
    await eapPage.fillEmploymentInformation(testData.employment);
    
    // Verify no error messages
    const hasError = await eapPage.hasErrorMessage();
    expect(hasError).toBeFalsy();
    logger.assertion('Employment information filled without errors');
  });

  test('should complete career advancement application @regression', async () => {
    logger.testStart('EAP career advancement application test');
    
    const testData = dataManager.getFormData('EAP', 'default');
    await eapPage.fillCompleteApplication(testData);
    
    // Try to save the application
    await eapPage.clickSave();
    
    // Verify success or no errors
    const hasError = await eapPage.hasErrorMessage();
    const hasSuccess = await eapPage.hasSuccessMessage();
    
    if (hasSuccess) {
      const successMessage = await eapPage.getSuccessMessage();
      logger.assertion(`Career advancement application saved: ${successMessage}`);
    } else {
      expect(hasError).toBeFalsy();
      logger.assertion('Career advancement application saved without errors');
    }
  });

  test('should complete apprenticeship application @regression', async () => {
    logger.testStart('EAP apprenticeship application test');
    
    const testData = dataManager.getFormData('EAP', 'apprenticeship');
    await eapPage.fillCompleteApplication(testData);
    
    // Fill apprenticeship specific information
    if (testData.apprenticeship) {
      await eapPage.fillApprenticeshipInformation(testData.apprenticeship);
    }
    
    // Try to save the application
    await eapPage.clickSave();
    
    // Verify success or no errors
    const hasError = await eapPage.hasErrorMessage();
    expect(hasError).toBeFalsy();
    logger.assertion('Apprenticeship application completed successfully');
  });

  test('should complete skills upgrade application @regression', async () => {
    logger.testStart('EAP skills upgrade application test');
    
    const testData = dataManager.getFormData('EAP', 'skills_upgrade');
    await eapPage.fillCompleteApplication(testData);
    
    // Try to save the application
    await eapPage.clickSave();
    
    // Verify success or no errors
    const hasError = await eapPage.hasErrorMessage();
    expect(hasError).toBeFalsy();
    logger.assertion('Skills upgrade application completed successfully');
  });

  test('should validate required fields for EAP @negative', async () => {
    logger.testStart('EAP required fields validation test');
    
    // Try to submit without filling required fields
    await eapPage.clickNext();
    
    // Should show validation errors or stay on same page
    const hasError = await eapPage.hasErrorMessage();
    if (hasError) {
      const errorMessage = await eapPage.getErrorMessage();
      logger.assertion(`Validation error displayed: ${errorMessage}`);
    } else {
      // Check if we're still on the same page
      await eapPage.verifyPageLoaded();
      logger.assertion('Form did not advance without required fields');
    }
  });

  test('should handle invalid employment status @negative', async () => {
    logger.testStart('Invalid employment status test');
    
    // This test would depend on the specific validation rules
    // For now, we'll test with minimal valid data
    const testData = {
      personalInfo: {
        firstName: 'Test',
        lastName: 'User',
        sin: '123456789',
        phone: '4165551234',
        email: '<EMAIL>'
      },
      employment: {
        status: 'Invalid Status' // This should be validated
      }
    };
    
    await eapPage.fillPersonalInformation(testData.personalInfo);
    
    // Try to fill employment with invalid status
    // This will depend on how the dropdown is implemented
    try {
      await eapPage.fillEmploymentInformation(testData.employment);
      await eapPage.clickNext();
      
      const hasError = await eapPage.hasErrorMessage();
      if (hasError) {
        const errorMessage = await eapPage.getErrorMessage();
        logger.assertion(`Employment status validation error: ${errorMessage}`);
      }
    } catch (error) {
      logger.assertion('Invalid employment status rejected by dropdown');
    }
  });

  test('should navigate between form sections @regression', async () => {
    logger.testStart('EAP form navigation test');
    
    // Fill some basic info
    const testData = dataManager.getFormData('EAP', 'default');
    await eapPage.fillPersonalInformation(testData.personalInfo);
    
    // Try to go to next step
    await eapPage.clickNext();
    
    // Verify we can go back
    await eapPage.clickPrevious();
    
    // Verify we're back to the form
    await eapPage.verifyPageLoaded();
    logger.assertion('EAP form navigation working correctly');
  });

  test('should save application progress @regression', async () => {
    logger.testStart('EAP save progress test');
    
    const testData = dataManager.getFormData('EAP', 'default');
    await eapPage.fillPersonalInformation(testData.personalInfo);
    
    // Save progress
    await eapPage.clickSave();
    
    // Verify save was successful
    const hasError = await eapPage.hasErrorMessage();
    const hasSuccess = await eapPage.hasSuccessMessage();
    
    expect(hasError).toBeFalsy();
    if (hasSuccess) {
      const successMessage = await eapPage.getSuccessMessage();
      logger.assertion(`EAP progress saved: ${successMessage}`);
    } else {
      logger.assertion('EAP progress saved without errors');
    }
  });

  test('should handle program goals and skills sections @regression', async () => {
    logger.testStart('EAP program goals and skills test');
    
    const testData = dataManager.getFormData('EAP', 'skills_upgrade');
    
    // Fill personal and employment info first
    await eapPage.fillPersonalInformation(testData.personalInfo);
    await eapPage.fillEmploymentInformation(testData.employment);
    
    // Fill program specific information
    await eapPage.fillProgramInformation(testData.program);
    
    // Verify no errors
    const hasError = await eapPage.hasErrorMessage();
    expect(hasError).toBeFalsy();
    logger.assertion('Program goals and skills information filled successfully');
  });

  test('should validate apprenticeship trade selection @regression', async () => {
    logger.testStart('Apprenticeship trade selection test');
    
    const testData = dataManager.getFormData('EAP', 'apprenticeship');
    
    // Fill basic information
    await eapPage.fillPersonalInformation(testData.personalInfo);
    await eapPage.fillEmploymentInformation(testData.employment);
    
    // Fill apprenticeship information
    if (testData.apprenticeship) {
      await eapPage.fillApprenticeshipInformation(testData.apprenticeship);
    }
    
    // Verify no errors
    const hasError = await eapPage.hasErrorMessage();
    expect(hasError).toBeFalsy();
    logger.assertion('Apprenticeship trade selection completed successfully');
  });

  test('should generate random test data for EAP application @regression', async () => {
    logger.testStart('EAP random test data generation test');
    
    const randomData = dataManager.generateRandomUserData();
    
    const applicationData = {
      personalInfo: {
        firstName: randomData.firstName,
        lastName: randomData.lastName,
        dateOfBirth: randomData.dateOfBirth,
        sin: randomData.sin,
        phone: randomData.phone,
        email: randomData.email
      },
      address: randomData.address,
      employment: {
        status: 'Employed',
        currentEmployer: randomData.employment.lastEmployer,
        jobTitle: 'Test Position',
        industry: randomData.employment.industry,
        hoursPerWeek: '40',
        hourlyWage: '20.00'
      },
      program: {
        serviceType: 'Career Advancement',
        goals: 'Advance in current career',
        currentSkills: 'Basic computer skills',
        desiredSkills: 'Advanced technical skills',
        targetIndustry: randomData.employment.industry
      }
    };
    
    await eapPage.fillCompleteApplication(applicationData);
    await eapPage.clickSave();
    
    const hasError = await eapPage.hasErrorMessage();
    expect(hasError).toBeFalsy();
    logger.assertion('Random EAP test data application completed successfully');
  });

  test('should handle different service types @regression', async () => {
    logger.testStart('EAP different service types test');
    
    const serviceTypes = ['Career Advancement', 'Skills Upgrading', 'Apprenticeship Placement'];
    
    for (const serviceType of serviceTypes) {
      logger.step(`Testing service type: ${serviceType}`);
      
      const testData = dataManager.getFormData('EAP', 'default');
      testData.program.serviceType = serviceType;
      
      await eapPage.fillPersonalInformation(testData.personalInfo);
      await eapPage.fillProgramInformation(testData.program);
      
      // Verify no errors for this service type
      const hasError = await eapPage.hasErrorMessage();
      expect(hasError).toBeFalsy();
      
      // Refresh page for next iteration
      await eapPage.refreshPage();
      await eapPage.verifyPageLoaded();
    }
    
    logger.assertion('All service types handled successfully');
  });
});
