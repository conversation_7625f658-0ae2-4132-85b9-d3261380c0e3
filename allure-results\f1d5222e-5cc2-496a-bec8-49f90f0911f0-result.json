{"uuid": "f1d5222e-5cc2-496a-bec8-49f90f0911f0", "name": "should handle invalid program codes gracefully @negative", "historyId": "bf552e89616dfe6bfa6098d4b87bc6fc:5bd835b0d6b1d4ada3b9f0db936e82c8", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "chromium"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "home.test.ts"}, {"name": "titlePath", "value": " > chromium > home.test.ts > RASP Home Page Tests - Basic"}, {"name": "tag", "value": "negative"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-36364-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "subSuite", "value": "RASP Home Page Tests - Basic"}], "links": [], "start": 1761577068520, "testCaseId": "bf552e89616dfe6bfa6098d4b87bc6fc", "fullName": "home.test.ts:183:7", "titlePath": ["home.test.ts", "RASP Home Page Tests - Basic"], "stop": 1761577068520}