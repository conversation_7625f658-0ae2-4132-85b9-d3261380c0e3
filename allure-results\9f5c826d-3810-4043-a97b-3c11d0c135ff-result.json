{"uuid": "9f5c826d-3810-4043-a97b-3c11d0c135ff", "name": "should handle invalid program codes gracefully @negative", "historyId": "bf552e89616dfe6bfa6098d4b87bc6fc:4151609b82fd60da52fe302128be4a3c", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Google Chrome"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "home.test.ts"}, {"name": "titlePath", "value": " > Google Chrome > home.test.ts > RASP Home Page Tests - Basic"}, {"name": "tag", "value": "negative"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-36364-worker-0"}, {"name": "parentSuite", "value": "Google Chrome"}, {"name": "subSuite", "value": "RASP Home Page Tests - Basic"}], "links": [], "start": 1761577068580, "testCaseId": "bf552e89616dfe6bfa6098d4b87bc6fc", "fullName": "home.test.ts:183:7", "titlePath": ["home.test.ts", "RASP Home Page Tests - Basic"], "stop": 1761577068580}