# Environment Configuration
TEST_ENV=QA
BASE_URL=https://qa4.employmentontario.labour.gov.on.ca

# RASP Application URLs
ESI_URL=https://qa4.employmentontario.labour.gov.on.ca/faec/?lang=en&programCode=ESI
EAP_URL=https://qa4.employmentontario.labour.gov.on.ca/faec/?lang=en&programCode=EAP

# Test Data
TEST_USERNAME=<EMAIL>
TEST_PASSWORD=TestPassword123!

# Browser Configuration
HEADLESS=false
BROWSER=chromium
VIEWPORT_WIDTH=1920
VIEWPORT_HEIGHT=1080

# Timeouts (in milliseconds)
ACTION_TIMEOUT=30000
NAVIGATION_TIMEOUT=30000
TEST_TIMEOUT=60000

# Reporting
REPORT_OUTPUT_DIR=reports
SCREENSHOT_ON_FAILURE=true
VIDEO_ON_FAILURE=true
TRACE_ON_RETRY=true

# Logging
LOG_LEVEL=info
LOG_TO_FILE=true
LOG_FILE_PATH=logs/test.log

# CI/CD Configuration
CI=false
PARALLEL_WORKERS=1
RETRY_COUNT=2

# Azure DevOps
AZURE_DEVOPS_PROJECT=RASP-Testing
AZURE_DEVOPS_ORGANIZATION=your-organization

# Test Categories
RUN_SMOKE_TESTS=true
RUN_REGRESSION_TESTS=true
RUN_API_TESTS=false
