# RASP Playwright TypeScript Testing Framework

A comprehensive end-to-end testing framework for the RASP (Rapid Access to Skills and Programs) application, specifically designed to test ESI (Employment Services Individual) and EAP (Employment Assistance Program) workflows.

## 🚀 Features

- **TypeScript Support**: Full TypeScript implementation with strict type checking
- **Page Object Model**: Organized and maintainable test structure
- **Multi-Browser Testing**: Support for Chromium, Firefox, and WebKit
- **Comprehensive Logging**: Winston-based logging with multiple levels and file output
- **Data Management**: JSON and Excel data support with random test data generation
- **CI/CD Integration**: Azure DevOps pipeline configuration
- **Rich Reporting**: HTML, JSON, JUnit, and Allure reports
- **Screenshot & Video**: Automatic capture on test failures
- **Parallel Execution**: Configurable parallel test execution
- **Environment Configuration**: Support for multiple test environments

## 📁 Project Structure

```
rasp-playwright-framework/
├── src/
│   ├── tests/                 # Test cases
│   │   ├── home.test.ts
│   │   ├── esi-application.test.ts
│   │   └── eap-application.test.ts
│   ├── pages/                 # Page Object Model classes
│   │   ├── base.page.ts
│   │   ├── home.page.ts
│   │   ├── esi-application.page.ts
│   │   └── eap-application.page.ts
│   ├── utils/                 # Utility classes
│   │   ├── logger.ts
│   │   ├── test-helpers.ts
│   │   └── data-manager.ts
│   ├── config/                # Configuration files
│   │   ├── test-config.ts
│   │   ├── global-setup.ts
│   │   └── global-teardown.ts
│   └── data/                  # Test data files
│       ├── credentials.json
│       ├── esi-forms.json
│       └── eap-forms.json
├── reports/                   # Generated test reports
├── logs/                      # Test execution logs
├── docs/                      # Documentation
├── playwright.config.ts       # Playwright configuration
├── tsconfig.json             # TypeScript configuration
├── azure-pipelines.yml       # Azure DevOps pipeline
└── package.json              # Dependencies and scripts
```

## 🛠️ Prerequisites

- **Node.js**: Version 16.0.0 or higher
- **npm**: Version 8.0.0 or higher
- **Git**: For version control

## 📦 Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd rasp-playwright-framework
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Install Playwright browsers**:
   ```bash
   npm run install:browsers
   ```

4. **Set up environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env file with your configuration
   ```

## 🏃‍♂️ Running Tests

### Basic Commands

```bash
# Run all tests
npm test

# Run tests in headed mode (visible browser)
npm run test:headed

# Run tests with UI mode
npm run test:ui

# Run specific test suites
npm run test:esi          # ESI application tests
npm run test:eap          # EAP application tests
npm run test:smoke        # Smoke tests only
npm run test:regression   # Regression tests only

# Debug tests
npm run test:debug

# Generate and view reports
npm run test:report
```

### Environment-Specific Testing

```bash
# Set environment variables
export TEST_ENV=QA
export BASE_URL=https://qa4.employmentontario.labour.gov.on.ca

# Run tests
npm test
```

## 🧪 Writing Tests

### Test Structure

Tests follow the Page Object Model pattern:

```typescript
import { test, expect } from '@playwright/test';
import { HomePage } from '../pages/home.page';
import { ESIApplicationPage } from '../pages/esi-application.page';

test.describe('ESI Application Tests', () => {
  let homePage: HomePage;
  let esiPage: ESIApplicationPage;

  test.beforeEach(async ({ page }) => {
    homePage = new HomePage(page);
    esiPage = new ESIApplicationPage(page);
    
    await homePage.navigateToESI();
    await esiPage.verifyPageLoaded();
  });

  test('should complete ESI application @smoke', async () => {
    // Test implementation
  });
});
```

### Test Categories

Use tags to categorize tests:

- `@smoke`: Critical functionality tests
- `@regression`: Comprehensive test suite
- `@negative`: Error handling and validation tests

### Data Management

```typescript
import { DataManager } from '../utils/data-manager';

const dataManager = new DataManager();

// Load predefined test data
const testData = dataManager.getFormData('ESI', 'default');

// Generate random test data
const randomData = dataManager.generateRandomUserData();
```

## 📊 Reporting

The framework generates multiple types of reports:

- **HTML Report**: `reports/html-report/index.html`
- **JSON Report**: `reports/test-results.json`
- **JUnit Report**: `reports/test-results.xml`
- **Allure Report**: `reports/allure-results/`

View reports:
```bash
npm run test:report
```

## 🔧 Configuration

### Environment Variables

Key environment variables in `.env`:

```env
# Test Environment
TEST_ENV=QA
BASE_URL=https://qa4.employmentontario.labour.gov.on.ca
ESI_URL=https://qa4.employmentontario.labour.gov.on.ca/faec/?lang=en&programCode=ESI
EAP_URL=https://qa4.employmentontario.labour.gov.on.ca/faec/?lang=en&programCode=EAP

# Browser Configuration
HEADLESS=false
BROWSER=chromium

# Test Execution
PARALLEL_WORKERS=1
RETRY_COUNT=2
```

### Playwright Configuration

Modify `playwright.config.ts` for:
- Browser settings
- Timeout configurations
- Reporter options
- Test directory structure

## 🚀 CI/CD Integration

### Azure DevOps

The framework includes a complete Azure DevOps pipeline (`azure-pipelines.yml`) that:

- Runs on multiple browsers
- Executes smoke tests on every PR
- Runs full regression suite on main branch
- Generates and publishes test reports
- Captures screenshots and videos on failures

### Pipeline Stages

1. **Build**: Setup environment and install dependencies
2. **Test**: Run smoke tests and application-specific tests
3. **Regression**: Full regression suite (scheduled/main branch)
4. **Reporting**: Consolidate and publish all test artifacts

## 📝 Logging

The framework uses Winston for comprehensive logging:

```typescript
import { Logger } from '../utils/logger';

const logger = Logger.getInstance();

logger.info('Test information');
logger.error('Test error', error);
logger.testStart('Test Name');
logger.testEnd('Test Name', 'PASSED');
```

Log files are generated in the `logs/` directory:
- `test.log`: All log messages
- `error.log`: Error messages only

## 🤝 Contributing

1. Create a feature branch
2. Write tests following the existing patterns
3. Ensure all tests pass
4. Update documentation if needed
5. Submit a pull request

## 📚 Additional Documentation

- [Getting Started Guide](docs/getting-started.md)
- [Test Writing Guidelines](docs/test-writing-guidelines.md)
- [Framework Architecture](docs/framework-architecture.md)
- [Troubleshooting Guide](docs/troubleshooting.md)

## 🐛 Troubleshooting

### Common Issues

1. **Browser Installation**: Run `npm run install:browsers`
2. **Permission Issues**: Ensure proper file permissions
3. **Network Issues**: Check firewall and proxy settings
4. **Environment Variables**: Verify `.env` configuration

### Debug Mode

Run tests in debug mode for step-by-step execution:
```bash
npm run test:debug
```

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support

For questions and support:
- Create an issue in the repository
- Contact the SDET team
- Check the documentation in the `docs/` folder
