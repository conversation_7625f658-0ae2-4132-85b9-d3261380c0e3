{"uuid": "ba01c5e4-38f6-418a-a3fd-a881669c3b76", "name": "should fill employment information for employed applicant @smoke", "historyId": "3684773979b995ace91c6963ffaac888:4151609b82fd60da52fe302128be4a3c", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Google Chrome"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "eap-application.test.ts"}, {"name": "titlePath", "value": " > Google Chrome > eap-application.test.ts > EAP Application Tests"}, {"name": "tag", "value": "smoke"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-49836-worker-0"}, {"name": "parentSuite", "value": "Google Chrome"}, {"name": "subSuite", "value": "EAP Application Tests"}], "links": [], "start": 1761576670081, "testCaseId": "3684773979b995ace91c6963ffaac888", "fullName": "eap-application.test.ts:62:7", "titlePath": ["eap-application.test.ts", "EAP Application Tests"], "stop": 1761576670081}