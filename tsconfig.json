{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020", "DOM"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false, "allowUnreachableCode": false, "allowUnusedLabels": false, "moduleResolution": "node", "baseUrl": "./", "paths": {"@pages/*": ["src/pages/*"], "@utils/*": ["src/utils/*"], "@config/*": ["src/config/*"], "@tests/*": ["src/tests/*"]}, "typeRoots": ["node_modules/@types"], "types": ["node", "@playwright/test"]}, "include": ["src/**/*.ts", "src/**/*.js", "playwright.config.ts"], "exclude": ["node_modules", "dist", "reports"], "ts-node": {"esm": true, "experimentalSpecifierResolution": "node"}}