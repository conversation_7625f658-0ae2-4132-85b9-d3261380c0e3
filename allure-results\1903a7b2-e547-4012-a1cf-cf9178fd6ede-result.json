{"uuid": "1903a7b2-e547-4012-a1cf-cf9178fd6ede", "name": "should fill personal information with valid data @smoke", "historyId": "d6be0fe5bd8f380ea72fde811175b871:96797789f4669ba5e9de53cebde9126b", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Chrome"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "eap-application.test.ts"}, {"name": "titlePath", "value": " > Mobile Chrome > eap-application.test.ts > EAP Application Tests"}, {"name": "tag", "value": "smoke"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-49836-worker-0"}, {"name": "parentSuite", "value": "Mobile Chrome"}, {"name": "subSuite", "value": "EAP Application Tests"}], "links": [], "start": 1761576670048, "testCaseId": "d6be0fe5bd8f380ea72fde811175b871", "fullName": "eap-application.test.ts:50:7", "titlePath": ["eap-application.test.ts", "EAP Application Tests"], "stop": 1761576670048}