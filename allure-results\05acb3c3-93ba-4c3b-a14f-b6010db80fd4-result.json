{"uuid": "05acb3c3-93ba-4c3b-a14f-b6010db80fd4", "name": "should display welcome message", "historyId": "636af9f23ac4d8bea4e6db7d0ec9cba1:4151609b82fd60da52fe302128be4a3c", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Google Chrome"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "home.test.ts"}, {"name": "titlePath", "value": " > Google Chrome > home.test.ts > RASP Home Page Tests - Basic"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-36364-worker-0"}, {"name": "parentSuite", "value": "Google Chrome"}, {"name": "subSuite", "value": "RASP Home Page Tests - Basic"}], "links": [], "start": 1761577068574, "testCaseId": "636af9f23ac4d8bea4e6db7d0ec9cba1", "fullName": "home.test.ts:102:7", "titlePath": ["home.test.ts", "RASP Home Page Tests - Basic"], "stop": 1761577068574}