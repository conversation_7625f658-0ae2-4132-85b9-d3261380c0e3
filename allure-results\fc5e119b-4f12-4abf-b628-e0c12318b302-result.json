{"uuid": "fc5e119b-4f12-4abf-b628-e0c12318b302", "name": "should fill personal information with valid data @smoke", "historyId": "d6be0fe5bd8f380ea72fde811175b871:5bd835b0d6b1d4ada3b9f0db936e82c8", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "chromium"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "eap-application.test.ts"}, {"name": "titlePath", "value": " > chromium > eap-application.test.ts > EAP Application Tests"}, {"name": "tag", "value": "smoke"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-49836-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "subSuite", "value": "EAP Application Tests"}], "links": [], "start": 1761576670024, "testCaseId": "d6be0fe5bd8f380ea72fde811175b871", "fullName": "eap-application.test.ts:50:7", "titlePath": ["eap-application.test.ts", "EAP Application Tests"], "stop": 1761576670024}