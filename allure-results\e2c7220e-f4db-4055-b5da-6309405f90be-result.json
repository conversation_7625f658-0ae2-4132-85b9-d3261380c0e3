{"uuid": "e2c7220e-f4db-4055-b5da-6309405f90be", "name": "should fill personal information with valid data @smoke", "historyId": "d6be0fe5bd8f380ea72fde811175b871:2d9eadd8e5698e677c4123f9b6b8cac6", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Microsoft Edge"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "eap-application.test.ts"}, {"name": "titlePath", "value": " > Microsoft Edge > eap-application.test.ts > EAP Application Tests"}, {"name": "tag", "value": "smoke"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-49836-worker-0"}, {"name": "parentSuite", "value": "Microsoft Edge"}, {"name": "subSuite", "value": "EAP Application Tests"}], "links": [], "start": 1761576670073, "testCaseId": "d6be0fe5bd8f380ea72fde811175b871", "fullName": "eap-application.test.ts:50:7", "titlePath": ["eap-application.test.ts", "EAP Application Tests"], "stop": 1761576670073}