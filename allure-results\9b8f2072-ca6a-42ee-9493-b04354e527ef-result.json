{"uuid": "9b8f2072-ca6a-42ee-9493-b04354e527ef", "name": "should fill address information with valid data @smoke", "historyId": "4c054fb698d8b75494c8f125516f9bbe:5bd835b0d6b1d4ada3b9f0db936e82c8", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "chromium"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "esi-application.test.ts"}, {"name": "titlePath", "value": " > chromium > esi-application.test.ts > ESI Application Tests"}, {"name": "tag", "value": "smoke"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-49836-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "subSuite", "value": "ESI Application Tests"}], "links": [], "start": 1761576670029, "testCaseId": "4c054fb698d8b75494c8f125516f9bbe", "fullName": "esi-application.test.ts:62:7", "titlePath": ["esi-application.test.ts", "ESI Application Tests"], "stop": 1761576670029}