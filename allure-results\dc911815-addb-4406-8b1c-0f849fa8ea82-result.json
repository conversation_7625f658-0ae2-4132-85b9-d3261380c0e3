{"uuid": "dc911815-addb-4406-8b1c-0f849fa8ea82", "name": "should fill employment information with valid data @smoke", "historyId": "fddf3058ae0350df7ba330116bf154fd:2d9eadd8e5698e677c4123f9b6b8cac6", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Microsoft Edge"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "esi-application.test.ts"}, {"name": "titlePath", "value": " > Microsoft Edge > esi-application.test.ts > ESI Application Tests"}, {"name": "tag", "value": "smoke"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-49836-worker-0"}, {"name": "parentSuite", "value": "Microsoft Edge"}, {"name": "subSuite", "value": "ESI Application Tests"}], "links": [], "start": 1761576670077, "testCaseId": "fddf3058ae0350df7ba330116bf154fd", "fullName": "esi-application.test.ts:74:7", "titlePath": ["esi-application.test.ts", "ESI Application Tests"], "stop": 1761576670077}