# Framework Architecture

This document provides a detailed overview of the RASP Playwright TypeScript testing framework architecture, design patterns, and core components.

## Architecture Overview

The framework follows a layered architecture pattern with clear separation of concerns:

```
┌─────────────────────────────────────────────────────────────┐
│                    Test Layer                               │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐│
│  │   Smoke Tests   │ │ Regression Tests│ │ Integration     ││
│  │                 │ │                 │ │ Tests           ││
│  └─────────────────┘ └─────────────────┘ └─────────────────┘│
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                 Page Object Layer                           │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐│
│  │   Home Page     │ │  ESI App Page   │ │  EAP App Page   ││
│  │                 │ │                 │ │                 ││
│  └─────────────────┘ └─────────────────┘ └─────────────────┘│
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                  Utility Layer                              │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐│
│  │     Logger      │ │  Test Helpers   │ │  Data Manager   ││
│  │                 │ │                 │ │                 ││
│  └─────────────────┘ └─────────────────┘ └─────────────────┘│
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│               Configuration Layer                           │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐│
│  │  Playwright     │ │  Environment    │ │  Test Data      ││
│  │  Config         │ │  Config         │ │  Config         ││
│  └─────────────────┘ └─────────────────┘ └─────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

## Core Components

### 1. Test Layer

#### Test Organization
```typescript
src/tests/
├── home.test.ts              # Home page functionality
├── esi-application.test.ts   # ESI application workflows
├── eap-application.test.ts   # EAP application workflows
└── integration/              # End-to-end integration tests
```

#### Test Categories
- **Smoke Tests**: Critical path validation
- **Regression Tests**: Comprehensive feature coverage
- **Integration Tests**: End-to-end workflows
- **Negative Tests**: Error handling and edge cases

### 2. Page Object Model Layer

#### Base Page Pattern
```typescript
export abstract class BasePage {
  protected page: Page;
  protected logger: Logger;
  protected helpers: TestHelpers;

  // Common functionality
  async navigateTo(url: string): Promise<void>
  async waitForPageLoad(): Promise<void>
  async takeScreenshot(name: string): Promise<string>
  
  // Abstract method for page-specific verification
  abstract async verifyPageLoaded(): Promise<void>;
}
```

#### Page Inheritance Hierarchy
```
BasePage
├── HomePage
├── ESIApplicationPage
└── EAPApplicationPage
```

#### Page Object Responsibilities
- **Element Interaction**: Click, type, select operations
- **Page Navigation**: URL navigation and routing
- **State Verification**: Page load and element state checks
- **Data Input**: Form filling and data entry
- **Error Handling**: Error message detection and handling

### 3. Utility Layer

#### Logger Component
```typescript
export class Logger {
  private static instance: Logger;
  private logger: winston.Logger;

  // Singleton pattern for global access
  public static getInstance(): Logger

  // Logging methods
  public info(message: string, meta?: any): void
  public error(message: string, error?: Error): void
  public testStart(testName: string): void
  public testEnd(testName: string, status: string): void
}
```

#### Test Helpers Component
```typescript
export class TestHelpers {
  // Safe interaction methods with retry logic
  async safeClick(page: Page, selector: string, retries: number): Promise<void>
  async safeType(page: Page, selector: string, text: string): Promise<void>
  
  // Wait strategies
  async waitForElement(page: Page, selector: string): Promise<Locator>
  async waitForPageLoad(page: Page): Promise<void>
  
  // Utility methods
  async takeScreenshot(page: Page, name: string): Promise<string>
  async scrollIntoView(page: Page, selector: string): Promise<void>
}
```

#### Data Manager Component
```typescript
export class DataManager {
  // Data loading methods
  loadJsonData(fileName: string): TestData
  loadExcelData(fileName: string, sheetName?: string): TestData[]
  
  // Environment-specific data
  getEnvironmentData(environment: string): TestData
  getFormData(programCode: 'ESI' | 'EAP', formType: string): TestData
  
  // Random data generation
  generateRandomUserData(): TestData
}
```

### 4. Configuration Layer

#### Playwright Configuration
```typescript
// playwright.config.ts
export default defineConfig({
  testDir: './src/tests',
  fullyParallel: true,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  
  use: {
    baseURL: process.env.BASE_URL,
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
  },
  
  projects: [
    { name: 'chromium', use: { ...devices['Desktop Chrome'] } },
    { name: 'firefox', use: { ...devices['Desktop Firefox'] } },
    { name: 'webkit', use: { ...devices['Desktop Safari'] } },
  ],
});
```

#### Environment Configuration
```typescript
// src/config/test-config.ts
export interface TestConfig {
  baseUrl: string;
  esiUrl: string;
  eapUrl: string;
  timeouts: TimeoutConfig;
  reporting: ReportingConfig;
  logging: LoggingConfig;
}

export const testConfig: TestConfig = {
  baseUrl: process.env.BASE_URL || 'default-url',
  // ... other configuration
};
```

## Design Patterns

### 1. Singleton Pattern
Used for Logger to ensure single instance across the framework:

```typescript
export class Logger {
  private static instance: Logger;
  
  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }
}
```

### 2. Page Object Model Pattern
Encapsulates page-specific functionality:

```typescript
export class ESIApplicationPage extends BasePage {
  // Selectors
  private readonly firstNameField = '[data-testid="first-name"]';
  
  // Actions
  async fillPersonalInformation(data: PersonalInfo): Promise<void> {
    await this.typeText(this.firstNameField, data.firstName);
  }
  
  // Verifications
  async verifyFormSubmitted(): Promise<void> {
    const hasSuccess = await this.hasSuccessMessage();
    expect(hasSuccess).toBeTruthy();
  }
}
```

### 3. Factory Pattern
Used for test data generation:

```typescript
export class DataManager {
  generateRandomUserData(): TestData {
    return {
      firstName: `TestUser${Date.now()}`,
      lastName: `LastName${Date.now()}`,
      email: `test${Date.now()}@example.com`,
      // ... other fields
    };
  }
}
```

### 4. Strategy Pattern
Used for different browser configurations:

```typescript
// playwright.config.ts
projects: [
  {
    name: 'chromium',
    use: { ...devices['Desktop Chrome'] },
  },
  {
    name: 'firefox',
    use: { ...devices['Desktop Firefox'] },
  },
]
```

## Data Flow

### Test Execution Flow
```
1. Global Setup
   ├── Environment validation
   ├── Directory creation
   └── Configuration loading

2. Test Initialization
   ├── Page object creation
   ├── Logger initialization
   └── Test data loading

3. Test Execution
   ├── Page navigation
   ├── User interactions
   ├── Data input
   └── Assertions

4. Test Cleanup
   ├── Screenshot capture (on failure)
   ├── Log finalization
   └── Resource cleanup

5. Global Teardown
   ├── Report generation
   ├── Log archiving
   └── Cleanup
```

### Data Management Flow
```
Test Data Sources
├── JSON Files (src/data/*.json)
├── Excel Files (src/data/*.xlsx)
├── Environment Variables (.env)
└── Generated Data (DataManager)
    ↓
Data Manager
├── Load and parse data
├── Environment-specific selection
├── Random data generation
└── Validation
    ↓
Page Objects
├── Form filling
├── Field validation
└── Data verification
    ↓
Test Assertions
├── Success verification
├── Error validation
└── Data persistence checks
```

## Error Handling Strategy

### Layered Error Handling
```typescript
// 1. Framework Level (BasePage)
async clickElement(selector: string): Promise<void> {
  try {
    await this.helpers.safeClick(this.page, selector);
  } catch (error) {
    this.logger.error(`Failed to click element: ${selector}`, error);
    await this.takeScreenshot(`error-click-${selector}`);
    throw error;
  }
}

// 2. Page Level (Specific Pages)
async fillPersonalInformation(data: PersonalInfo): Promise<void> {
  try {
    await this.typeText(this.firstNameField, data.firstName);
  } catch (error) {
    this.logger.error('Failed to fill personal information', error);
    throw new Error(`Personal information filling failed: ${error.message}`);
  }
}

// 3. Test Level
test('should handle form submission', async () => {
  try {
    await esiPage.fillCompleteApplication(testData);
    await esiPage.clickSubmit();
  } catch (error) {
    logger.error('Test failed during form submission', error);
    throw error;
  }
});
```

## Reporting Architecture

### Multi-Format Reporting
```typescript
// playwright.config.ts
reporter: [
  ['html', { outputFolder: 'reports/html-report' }],
  ['json', { outputFile: 'reports/test-results.json' }],
  ['junit', { outputFile: 'reports/test-results.xml' }],
  ['allure-playwright', { outputFolder: 'reports/allure-results' }],
]
```

### Report Data Flow
```
Test Execution
    ↓
Playwright Reporters
├── HTML Reporter → reports/html-report/
├── JSON Reporter → reports/test-results.json
├── JUnit Reporter → reports/test-results.xml
└── Allure Reporter → reports/allure-results/
    ↓
CI/CD Pipeline
├── Artifact Publishing
├── Test Result Analysis
└── Notification Systems
```

## Scalability Considerations

### Horizontal Scaling
- **Parallel Execution**: Configurable worker processes
- **Browser Distribution**: Multiple browser projects
- **Test Sharding**: Automatic test distribution

### Vertical Scaling
- **Modular Architecture**: Independent component scaling
- **Lazy Loading**: On-demand resource loading
- **Memory Management**: Efficient resource cleanup

### Maintenance Scaling
- **Code Reusability**: Shared base classes and utilities
- **Configuration Management**: Environment-specific configs
- **Documentation**: Self-documenting code patterns

## Security Considerations

### Credential Management
```typescript
// Secure credential handling
const credentials = dataManager.getUserCredentials('default');
// Never log sensitive data
logger.info('Logging in user', { username: credentials.username });
```

### Environment Isolation
```typescript
// Environment-specific configurations
const config = getEnvironmentConfig(process.env.TEST_ENV);
// Prevent cross-environment data leakage
```

## Performance Optimization

### Resource Management
- **Browser Reuse**: Shared browser contexts
- **Parallel Execution**: Optimized worker allocation
- **Memory Cleanup**: Proper resource disposal

### Network Optimization
- **Request Interception**: Blocking unnecessary resources
- **Cache Management**: Efficient cache strategies
- **Timeout Optimization**: Balanced wait strategies

This architecture provides a robust, scalable, and maintainable foundation for automated testing of the RASP application while following industry best practices and design patterns.
