{"uuid": "385062fa-673a-4f15-a009-3f3cbb74547b", "name": "should handle browser back navigation @regression", "historyId": "2d16f64fb5e901f85d4ca02bdf65c012:b444eb0fbe6390c71e68b51dd25701fc", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "firefox"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "home.test.ts"}, {"name": "titlePath", "value": " > firefox > home.test.ts > RASP Home Page Tests - Basic"}, {"name": "tag", "value": "regression"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-36364-worker-0"}, {"name": "parentSuite", "value": "firefox"}, {"name": "subSuite", "value": "RASP Home Page Tests - Basic"}], "links": [], "start": 1761577068527, "testCaseId": "2d16f64fb5e901f85d4ca02bdf65c012", "fullName": "home.test.ts:152:7", "titlePath": ["home.test.ts", "RASP Home Page Tests - Basic"], "stop": 1761577068527}