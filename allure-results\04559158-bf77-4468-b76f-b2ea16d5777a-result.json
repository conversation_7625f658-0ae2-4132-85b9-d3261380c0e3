{"uuid": "04559158-bf77-4468-b76f-b2ea16d5777a", "name": "should maintain session across page navigation @regression", "historyId": "b0b0ba68ae5215d068f25ad2f71bb580:2d9eadd8e5698e677c4123f9b6b8cac6", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Microsoft Edge"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "home.test.ts"}, {"name": "titlePath", "value": " > Microsoft Edge > home.test.ts > RASP Home Page Tests - Basic"}, {"name": "tag", "value": "regression"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-36364-worker-0"}, {"name": "parentSuite", "value": "Microsoft Edge"}, {"name": "subSuite", "value": "RASP Home Page Tests - Basic"}], "links": [], "start": 1761577068569, "testCaseId": "b0b0ba68ae5215d068f25ad2f71bb580", "fullName": "home.test.ts:167:7", "titlePath": ["home.test.ts", "RASP Home Page Tests - Basic"], "stop": 1761577068569}