{"uuid": "3975e01c-d9d9-45eb-9f1c-88f01b6a4453", "name": "should fill address information with valid data @smoke", "historyId": "4c054fb698d8b75494c8f125516f9bbe:b444eb0fbe6390c71e68b51dd25701fc", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "firefox"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "esi-application.test.ts"}, {"name": "titlePath", "value": " > firefox > esi-application.test.ts > ESI Application Tests"}, {"name": "tag", "value": "smoke"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-49836-worker-0"}, {"name": "parentSuite", "value": "firefox"}, {"name": "subSuite", "value": "ESI Application Tests"}], "links": [], "start": 1761576670036, "testCaseId": "4c054fb698d8b75494c8f125516f9bbe", "fullName": "esi-application.test.ts:62:7", "titlePath": ["esi-application.test.ts", "ESI Application Tests"], "stop": 1761576670036}