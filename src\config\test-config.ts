import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

export interface TestConfig {
  baseUrl: string;
  esiUrl: string;
  eapUrl: string;
  testUsername: string;
  testPassword: string;
  browser: string;
  headless: boolean;
  viewport: {
    width: number;
    height: number;
  };
  timeouts: {
    action: number;
    navigation: number;
    test: number;
  };
  reporting: {
    outputDir: string;
    screenshotOnFailure: boolean;
    videoOnFailure: boolean;
    traceOnRetry: boolean;
  };
  logging: {
    level: string;
    toFile: boolean;
    filePath: string;
  };
  ci: {
    enabled: boolean;
    parallelWorkers: number;
    retryCount: number;
  };
}

export const testConfig: TestConfig = {
  baseUrl: process.env.BASE_URL || 'https://qa4.employmentontario.labour.gov.on.ca',
  esiUrl: process.env.ESI_URL || 'https://qa4.employmentontario.labour.gov.on.ca/faec/?lang=en&programCode=ESI',
  eapUrl: process.env.EAP_URL || 'https://qa4.employmentontario.labour.gov.on.ca/faec/?lang=en&programCode=EAP',
  testUsername: process.env.TEST_USERNAME || '<EMAIL>',
  testPassword: process.env.TEST_PASSWORD || 'TestPassword123!',
  browser: process.env.BROWSER || 'chromium',
  headless: process.env.HEADLESS !== 'false',
  viewport: {
    width: parseInt(process.env.VIEWPORT_WIDTH || '1920'),
    height: parseInt(process.env.VIEWPORT_HEIGHT || '1080'),
  },
  timeouts: {
    action: parseInt(process.env.ACTION_TIMEOUT || '30000'),
    navigation: parseInt(process.env.NAVIGATION_TIMEOUT || '30000'),
    test: parseInt(process.env.TEST_TIMEOUT || '60000'),
  },
  reporting: {
    outputDir: process.env.REPORT_OUTPUT_DIR || 'reports',
    screenshotOnFailure: process.env.SCREENSHOT_ON_FAILURE !== 'false',
    videoOnFailure: process.env.VIDEO_ON_FAILURE !== 'false',
    traceOnRetry: process.env.TRACE_ON_RETRY !== 'false',
  },
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    toFile: process.env.LOG_TO_FILE !== 'false',
    filePath: process.env.LOG_FILE_PATH || 'logs/test.log',
  },
  ci: {
    enabled: process.env.CI === 'true',
    parallelWorkers: parseInt(process.env.PARALLEL_WORKERS || '1'),
    retryCount: parseInt(process.env.RETRY_COUNT || '2'),
  },
};

export const getEnvironment = (): string => {
  return process.env.TEST_ENV || 'QA';
};

export const isCI = (): boolean => {
  return testConfig.ci.enabled;
};

export const getBaseUrl = (): string => {
  return testConfig.baseUrl;
};

export const getESIUrl = (): string => {
  return testConfig.esiUrl;
};

export const getEAPUrl = (): string => {
  return testConfig.eapUrl;
};
