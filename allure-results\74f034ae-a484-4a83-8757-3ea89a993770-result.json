{"uuid": "74f034ae-a484-4a83-8757-3ea89a993770", "name": "should maintain session across page navigation @regression", "historyId": "b0b0ba68ae5215d068f25ad2f71bb580:4151609b82fd60da52fe302128be4a3c", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Google Chrome"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "home.test.ts"}, {"name": "titlePath", "value": " > Google Chrome > home.test.ts > RASP Home Page Tests - Basic"}, {"name": "tag", "value": "regression"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-36364-worker-0"}, {"name": "parentSuite", "value": "Google Chrome"}, {"name": "subSuite", "value": "RASP Home Page Tests - Basic"}], "links": [], "start": 1761577068579, "testCaseId": "b0b0ba68ae5215d068f25ad2f71bb580", "fullName": "home.test.ts:167:7", "titlePath": ["home.test.ts", "RASP Home Page Tests - Basic"], "stop": 1761577068579}