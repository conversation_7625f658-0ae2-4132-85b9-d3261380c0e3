{"uuid": "c5c6ea8a-491c-4607-9b11-77d27c16b48e", "name": "should handle page refresh correctly @regression", "historyId": "65ede488e88d5e46930bd195693a89fe:b444eb0fbe6390c71e68b51dd25701fc", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "firefox"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "home.test.ts"}, {"name": "titlePath", "value": " > firefox > home.test.ts > RASP Home Page Tests - Basic"}, {"name": "tag", "value": "regression"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-36364-worker-0"}, {"name": "parentSuite", "value": "firefox"}, {"name": "subSuite", "value": "RASP Home Page Tests - Basic"}], "links": [], "start": 1761577068526, "testCaseId": "65ede488e88d5e46930bd195693a89fe", "fullName": "home.test.ts:135:7", "titlePath": ["home.test.ts", "RASP Home Page Tests - Basic"], "stop": 1761577068526}