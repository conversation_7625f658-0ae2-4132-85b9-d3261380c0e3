{"uuid": "4287d4fb-a4fe-4fb1-bd77-1a97e0f870db", "name": "should handle page refresh correctly @regression", "historyId": "65ede488e88d5e46930bd195693a89fe:96797789f4669ba5e9de53cebde9126b", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Chrome"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "home.test.ts"}, {"name": "titlePath", "value": " > Mobile Chrome > home.test.ts > RASP Home Page Tests - Basic"}, {"name": "tag", "value": "regression"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-36364-worker-0"}, {"name": "parentSuite", "value": "Mobile Chrome"}, {"name": "subSuite", "value": "RASP Home Page Tests - Basic"}], "links": [], "start": 1761577068549, "testCaseId": "65ede488e88d5e46930bd195693a89fe", "fullName": "home.test.ts:135:7", "titlePath": ["home.test.ts", "RASP Home Page Tests - Basic"], "stop": 1761577068549}