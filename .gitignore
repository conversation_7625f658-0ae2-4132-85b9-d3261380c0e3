# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
build/

# Test results and reports
reports/
test-results/
playwright-report/
coverage/

# Logs
logs/
*.log

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Playwright
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/

# Screenshots and videos
screenshots/
videos/
traces/

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Package manager lock files (keep only one)
package-lock.json
yarn.lock
pnpm-lock.yaml
