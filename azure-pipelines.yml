# Azure DevOps Pipeline for RASP Playwright Testing Framework
# This pipeline runs automated tests for ESI and EAP programs

trigger:
  branches:
    include:
      - main
      - develop
      - feature/*
  paths:
    include:
      - src/**
      - playwright.config.ts
      - package.json
      - azure-pipelines.yml

pr:
  branches:
    include:
      - main
      - develop
  paths:
    include:
      - src/**
      - playwright.config.ts
      - package.json

variables:
  # Test Environment Configuration
  TEST_ENV: 'QA'
  BASE_URL: 'https://qa4.employmentontario.labour.gov.on.ca'
  ESI_URL: 'https://qa4.employmentontario.labour.gov.on.ca/faec/?lang=en&programCode=ESI'
  EAP_URL: 'https://qa4.employmentontario.labour.gov.on.ca/faec/?lang=en&programCode=EAP'
  
  # Browser Configuration
  HEADLESS: 'true'
  BROWSER: 'chromium'
  
  # CI Configuration
  CI: 'true'
  PARALLEL_WORKERS: '2'
  RETRY_COUNT: '2'
  
  # Reporting
  SCREENSHOT_ON_FAILURE: 'true'
  VIDEO_ON_FAILURE: 'true'
  TRACE_ON_RETRY: 'true'

stages:
  - stage: Build
    displayName: 'Build and Setup'
    jobs:
      - job: Setup
        displayName: 'Setup Environment'
        pool:
          vmImage: 'ubuntu-latest'
        
        steps:
          - task: NodeTool@0
            displayName: 'Install Node.js'
            inputs:
              versionSpec: '18.x'
          
          - task: Cache@2
            displayName: 'Cache node_modules'
            inputs:
              key: 'npm | "$(Agent.OS)" | package-lock.json'
              restoreKeys: |
                npm | "$(Agent.OS)"
              path: 'node_modules'
          
          - script: |
              npm ci
            displayName: 'Install Dependencies'
          
          - script: |
              npx playwright install --with-deps
            displayName: 'Install Playwright Browsers'
          
          - script: |
              npm run lint
            displayName: 'Run ESLint'
            continueOnError: true
          
          - script: |
              npm run build
            displayName: 'Build TypeScript'
            continueOnError: true

  - stage: Test
    displayName: 'Run Tests'
    dependsOn: Build
    jobs:
      - job: SmokeTests
        displayName: 'Smoke Tests'
        pool:
          vmImage: 'ubuntu-latest'
        
        steps:
          - task: NodeTool@0
            displayName: 'Install Node.js'
            inputs:
              versionSpec: '18.x'
          
          - task: Cache@2
            displayName: 'Restore node_modules'
            inputs:
              key: 'npm | "$(Agent.OS)" | package-lock.json'
              restoreKeys: |
                npm | "$(Agent.OS)"
              path: 'node_modules'
          
          - script: |
              npm ci
            displayName: 'Install Dependencies'
          
          - script: |
              npx playwright install --with-deps chromium
            displayName: 'Install Chromium'
          
          - script: |
              npm run test:smoke
            displayName: 'Run Smoke Tests'
            env:
              TEST_ENV: $(TEST_ENV)
              BASE_URL: $(BASE_URL)
              ESI_URL: $(ESI_URL)
              EAP_URL: $(EAP_URL)
              HEADLESS: $(HEADLESS)
              CI: $(CI)
              PARALLEL_WORKERS: $(PARALLEL_WORKERS)
              RETRY_COUNT: $(RETRY_COUNT)
          
          - task: PublishTestResults@2
            displayName: 'Publish Smoke Test Results'
            condition: always()
            inputs:
              testResultsFormat: 'JUnit'
              testResultsFiles: 'reports/test-results.xml'
              testRunTitle: 'RASP Smoke Tests'
              mergeTestResults: true
          
          - task: PublishHtmlReport@1
            displayName: 'Publish HTML Report'
            condition: always()
            inputs:
              reportDir: 'reports/html-report'
              tabName: 'Smoke Test Report'

      - job: ESITests
        displayName: 'ESI Application Tests'
        pool:
          vmImage: 'ubuntu-latest'
        
        steps:
          - task: NodeTool@0
            displayName: 'Install Node.js'
            inputs:
              versionSpec: '18.x'
          
          - script: |
              npm ci
              npx playwright install --with-deps chromium
            displayName: 'Setup Environment'
          
          - script: |
              npm run test:esi
            displayName: 'Run ESI Tests'
            env:
              TEST_ENV: $(TEST_ENV)
              BASE_URL: $(BASE_URL)
              ESI_URL: $(ESI_URL)
              HEADLESS: $(HEADLESS)
              CI: $(CI)
          
          - task: PublishTestResults@2
            displayName: 'Publish ESI Test Results'
            condition: always()
            inputs:
              testResultsFormat: 'JUnit'
              testResultsFiles: 'reports/test-results.xml'
              testRunTitle: 'RASP ESI Tests'

      - job: EAPTests
        displayName: 'EAP Application Tests'
        pool:
          vmImage: 'ubuntu-latest'
        
        steps:
          - task: NodeTool@0
            displayName: 'Install Node.js'
            inputs:
              versionSpec: '18.x'
          
          - script: |
              npm ci
              npx playwright install --with-deps chromium
            displayName: 'Setup Environment'
          
          - script: |
              npm run test:eap
            displayName: 'Run EAP Tests'
            env:
              TEST_ENV: $(TEST_ENV)
              BASE_URL: $(BASE_URL)
              EAP_URL: $(EAP_URL)
              HEADLESS: $(HEADLESS)
              CI: $(CI)
          
          - task: PublishTestResults@2
            displayName: 'Publish EAP Test Results'
            condition: always()
            inputs:
              testResultsFormat: 'JUnit'
              testResultsFiles: 'reports/test-results.xml'
              testRunTitle: 'RASP EAP Tests'

  - stage: RegressionTests
    displayName: 'Regression Tests'
    dependsOn: Test
    condition: and(succeeded(), or(eq(variables['Build.SourceBranch'], 'refs/heads/main'), eq(variables['Build.Reason'], 'Schedule')))
    jobs:
      - job: FullRegression
        displayName: 'Full Regression Suite'
        pool:
          vmImage: 'ubuntu-latest'
        strategy:
          matrix:
            Chrome:
              BROWSER: 'chromium'
            Firefox:
              BROWSER: 'firefox'
            Safari:
              BROWSER: 'webkit'
        
        steps:
          - task: NodeTool@0
            displayName: 'Install Node.js'
            inputs:
              versionSpec: '18.x'
          
          - script: |
              npm ci
              npx playwright install --with-deps $(BROWSER)
            displayName: 'Setup Environment'
          
          - script: |
              npm run test:regression
            displayName: 'Run Regression Tests'
            env:
              TEST_ENV: $(TEST_ENV)
              BASE_URL: $(BASE_URL)
              BROWSER: $(BROWSER)
              HEADLESS: $(HEADLESS)
              CI: $(CI)
              PARALLEL_WORKERS: '1'
          
          - task: PublishTestResults@2
            displayName: 'Publish Regression Test Results'
            condition: always()
            inputs:
              testResultsFormat: 'JUnit'
              testResultsFiles: 'reports/test-results.xml'
              testRunTitle: 'RASP Regression Tests - $(BROWSER)'

  - stage: Reporting
    displayName: 'Generate Reports'
    dependsOn: 
      - Test
      - RegressionTests
    condition: always()
    jobs:
      - job: ConsolidateReports
        displayName: 'Consolidate Test Reports'
        pool:
          vmImage: 'ubuntu-latest'
        
        steps:
          - task: DownloadBuildArtifacts@0
            displayName: 'Download Test Artifacts'
            inputs:
              buildType: 'current'
              downloadType: 'all'
              downloadPath: '$(System.ArtifactsDirectory)'
          
          - task: PublishBuildArtifacts@1
            displayName: 'Publish Screenshots'
            condition: always()
            inputs:
              pathToPublish: 'screenshots'
              artifactName: 'test-screenshots'
          
          - task: PublishBuildArtifacts@1
            displayName: 'Publish Videos'
            condition: always()
            inputs:
              pathToPublish: 'videos'
              artifactName: 'test-videos'
          
          - task: PublishBuildArtifacts@1
            displayName: 'Publish Traces'
            condition: always()
            inputs:
              pathToPublish: 'traces'
              artifactName: 'test-traces'
          
          - task: PublishBuildArtifacts@1
            displayName: 'Publish Logs'
            condition: always()
            inputs:
              pathToPublish: 'logs'
              artifactName: 'test-logs'

# Scheduled runs for nightly regression testing
schedules:
  - cron: "0 2 * * *"
    displayName: 'Nightly Regression Tests'
    branches:
      include:
        - main
    always: true
