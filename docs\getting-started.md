# Getting Started with RASP Playwright Framework

This guide will help you set up and start using the RASP Playwright TypeScript testing framework.

## Prerequisites

Before you begin, ensure you have the following installed on your system:

### Required Software

1. **Node.js** (version 16.0.0 or higher)
   - Download from [nodejs.org](https://nodejs.org/)
   - Verify installation: `node --version`

2. **npm** (version 8.0.0 or higher)
   - Usually comes with Node.js
   - Verify installation: `npm --version`

3. **Git**
   - Download from [git-scm.com](https://git-scm.com/)
   - Verify installation: `git --version`

4. **Visual Studio Code** (recommended)
   - Download from [code.visualstudio.com](https://code.visualstudio.com/)
   - Install Playwright extension for better development experience

## Installation Steps

### 1. Clone the Repository

```bash
git clone <repository-url>
cd rasp-playwright-framework
```

### 2. Install Dependencies

```bash
npm install
```

This will install all required packages including:
- Playwright
- TypeScript
- Winston (logging)
- ESLint and Prettier
- Testing utilities

### 3. Install Playwright Browsers

```bash
npm run install:browsers
```

This downloads the browser binaries needed for testing (Chromium, Firefox, WebKit).

### 4. Environment Configuration

Copy the example environment file and configure it:

```bash
cp .env.example .env
```

Edit the `.env` file with your specific configuration:

```env
# Test Environment
TEST_ENV=QA
BASE_URL=https://qa4.employmentontario.labour.gov.on.ca

# RASP Application URLs
ESI_URL=https://qa4.employmentontario.labour.gov.on.ca/faec/?lang=en&programCode=ESI
EAP_URL=https://qa4.employmentontario.labour.gov.on.ca/faec/?lang=en&programCode=EAP

# Browser Configuration
HEADLESS=false
BROWSER=chromium

# Test Execution
PARALLEL_WORKERS=1
RETRY_COUNT=2
```

### 5. Verify Installation

Run a quick test to verify everything is working:

```bash
npm run test:smoke
```

## First Test Run

### Running Basic Tests

1. **Run all tests**:
   ```bash
   npm test
   ```

2. **Run tests with visible browser** (helpful for debugging):
   ```bash
   npm run test:headed
   ```

3. **Run specific test suites**:
   ```bash
   npm run test:esi    # ESI application tests
   npm run test:eap    # EAP application tests
   ```

### Understanding Test Output

When you run tests, you'll see:

1. **Console Output**: Real-time test execution status
2. **Log Files**: Detailed logs in `logs/test.log`
3. **Reports**: HTML report in `reports/html-report/index.html`
4. **Screenshots**: Failure screenshots in `screenshots/`

## Project Structure Overview

```
rasp-playwright-framework/
├── src/
│   ├── tests/           # Your test files
│   ├── pages/           # Page Object Model classes
│   ├── utils/           # Helper utilities
│   ├── config/          # Configuration files
│   └── data/            # Test data files
├── reports/             # Generated test reports
├── logs/                # Test execution logs
├── docs/                # Documentation
└── playwright.config.ts # Main configuration
```

## Writing Your First Test

### 1. Create a Test File

Create a new test file in `src/tests/`:

```typescript
// src/tests/my-first.test.ts
import { test, expect } from '@playwright/test';
import { HomePage } from '../pages/home.page';

test.describe('My First Test Suite', () => {
  test('should load home page', async ({ page }) => {
    const homePage = new HomePage(page);
    
    await homePage.navigateToHome();
    await homePage.verifyPageLoaded();
    
    const title = await homePage.getPageTitle();
    expect(title).toBeTruthy();
  });
});
```

### 2. Run Your Test

```bash
npx playwright test src/tests/my-first.test.ts
```

## Development Workflow

### 1. IDE Setup (VS Code)

Install recommended extensions:
- Playwright Test for VS Code
- TypeScript and JavaScript Language Features
- ESLint
- Prettier

### 2. Code Quality

The framework includes automated code quality tools:

```bash
# Run linting
npm run lint

# Fix linting issues
npm run lint:fix

# Format code
npm run format
```

### 3. Debugging Tests

For debugging failing tests:

```bash
# Run in debug mode
npm run test:debug

# Run with UI mode
npm run test:ui

# Run specific test with headed browser
npx playwright test src/tests/specific.test.ts --headed
```

## Common Commands Reference

### Test Execution
```bash
npm test                    # Run all tests
npm run test:headed         # Run with visible browser
npm run test:debug          # Run in debug mode
npm run test:ui             # Run with Playwright UI
npm run test:smoke          # Run smoke tests only
npm run test:regression     # Run regression tests
```

### Reporting
```bash
npm run test:report         # Open HTML report
```

### Maintenance
```bash
npm run clean               # Clean reports and artifacts
npm run build               # Build TypeScript
npm run lint                # Run ESLint
npm run format              # Format code with Prettier
```

## Next Steps

1. **Read the Test Writing Guidelines**: [test-writing-guidelines.md](test-writing-guidelines.md)
2. **Understand the Framework Architecture**: [framework-architecture.md](framework-architecture.md)
3. **Explore Example Tests**: Look at existing tests in `src/tests/`
4. **Set up CI/CD**: Configure Azure DevOps pipeline
5. **Customize Configuration**: Modify `playwright.config.ts` for your needs

## Getting Help

If you encounter issues:

1. **Check the logs**: Look in `logs/test.log` for detailed information
2. **Review documentation**: Check other files in the `docs/` folder
3. **Run diagnostics**: Use `npm run test:debug` for step-by-step execution
4. **Check configuration**: Verify your `.env` file settings

## Troubleshooting Common Issues

### Browser Installation Issues
```bash
# Reinstall browsers
npm run install:browsers

# Install specific browser
npx playwright install chromium
```

### Permission Issues
```bash
# On Windows, run as administrator
# On macOS/Linux, check file permissions
chmod +x node_modules/.bin/playwright
```

### Network Issues
- Check firewall settings
- Verify proxy configuration
- Ensure access to test URLs

### Environment Issues
- Verify Node.js version: `node --version`
- Clear npm cache: `npm cache clean --force`
- Delete node_modules and reinstall: `rm -rf node_modules && npm install`

You're now ready to start testing with the RASP Playwright framework!
