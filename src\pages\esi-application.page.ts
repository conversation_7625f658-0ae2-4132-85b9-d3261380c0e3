import { Page, expect } from '@playwright/test';
import { BasePage } from './base.page';

export class ESIApplicationPage extends BasePage {
  // Form section selectors
  private readonly personalInfoSection = '[data-testid="personal-info"], .personal-information, #personal-info';
  private readonly addressSection = '[data-testid="address"], .address-information, #address-info';
  private readonly employmentSection = '[data-testid="employment"], .employment-information, #employment-info';
  private readonly educationSection = '[data-testid="education"], .education-information, #education-info';
  private readonly programSection = '[data-testid="program"], .program-information, #program-info';

  // Personal Information fields
  private readonly firstNameField = 'input[name="firstName"], #firstName, [data-testid="first-name"]';
  private readonly lastNameField = 'input[name="lastName"], #lastName, [data-testid="last-name"]';
  private readonly middleNameField = 'input[name="middleName"], #middleName, [data-testid="middle-name"]';
  private readonly dateOfBirthField = 'input[name="dateOfBirth"], #dateOfBirth, [data-testid="date-of-birth"]';
  private readonly sinField = 'input[name="sin"], #sin, [data-testid="sin"]';
  private readonly phoneField = 'input[name="phone"], #phone, [data-testid="phone"]';
  private readonly emailField = 'input[name="email"], #email, [data-testid="email"]';
  private readonly genderSelect = 'select[name="gender"], #gender, [data-testid="gender"]';
  private readonly maritalStatusSelect = 'select[name="maritalStatus"], #maritalStatus, [data-testid="marital-status"]';

  // Address fields
  private readonly streetField = 'input[name="street"], #street, [data-testid="street"]';
  private readonly unitField = 'input[name="unit"], #unit, [data-testid="unit"]';
  private readonly cityField = 'input[name="city"], #city, [data-testid="city"]';
  private readonly provinceSelect = 'select[name="province"], #province, [data-testid="province"]';
  private readonly postalCodeField = 'input[name="postalCode"], #postalCode, [data-testid="postal-code"]';
  private readonly countrySelect = 'select[name="country"], #country, [data-testid="country"]';

  // Employment fields
  private readonly employmentStatusSelect = 'select[name="employmentStatus"], #employmentStatus, [data-testid="employment-status"]';
  private readonly lastEmployerField = 'input[name="lastEmployer"], #lastEmployer, [data-testid="last-employer"]';
  private readonly jobTitleField = 'input[name="jobTitle"], #jobTitle, [data-testid="job-title"]';
  private readonly industrySelect = 'select[name="industry"], #industry, [data-testid="industry"]';
  private readonly lastWorkDateField = 'input[name="lastWorkDate"], #lastWorkDate, [data-testid="last-work-date"]';

  // Navigation buttons
  private readonly nextButton = 'button[data-testid="next"], .btn-next, button:has-text("Next")';
  private readonly previousButton = 'button[data-testid="previous"], .btn-previous, button:has-text("Previous")';
  private readonly saveButton = 'button[data-testid="save"], .btn-save, button:has-text("Save")';
  private readonly submitButton = 'button[data-testid="submit"], .btn-submit, button:has-text("Submit")';

  // Progress indicator
  private readonly progressBar = '.progress-bar, [data-testid="progress"], .step-indicator';
  private readonly currentStep = '.current-step, [data-testid="current-step"], .active-step';

  constructor(page: Page) {
    super(page);
  }

  /**
   * Fill personal information section
   */
  async fillPersonalInformation(personalInfo: any): Promise<void> {
    this.logger.action('Filling personal information section');

    if (personalInfo.firstName) {
      await this.typeText(this.firstNameField, personalInfo.firstName);
    }

    if (personalInfo.lastName) {
      await this.typeText(this.lastNameField, personalInfo.lastName);
    }

    if (personalInfo.middleName && await this.isElementVisible(this.middleNameField)) {
      await this.typeText(this.middleNameField, personalInfo.middleName);
    }

    if (personalInfo.dateOfBirth) {
      await this.typeText(this.dateOfBirthField, personalInfo.dateOfBirth);
    }

    if (personalInfo.sin) {
      await this.typeText(this.sinField, personalInfo.sin);
    }

    if (personalInfo.phone) {
      await this.typeText(this.phoneField, personalInfo.phone);
    }

    if (personalInfo.email) {
      await this.typeText(this.emailField, personalInfo.email);
    }

    if (personalInfo.gender && await this.isElementVisible(this.genderSelect)) {
      await this.selectDropdown(this.genderSelect, personalInfo.gender);
    }

    if (personalInfo.maritalStatus && await this.isElementVisible(this.maritalStatusSelect)) {
      await this.selectDropdown(this.maritalStatusSelect, personalInfo.maritalStatus);
    }

    this.logger.info('Personal information filled successfully');
  }

  /**
   * Fill address information section
   */
  async fillAddressInformation(address: any): Promise<void> {
    this.logger.action('Filling address information section');

    if (address.street) {
      await this.typeText(this.streetField, address.street);
    }

    if (address.unit && await this.isElementVisible(this.unitField)) {
      await this.typeText(this.unitField, address.unit);
    }

    if (address.city) {
      await this.typeText(this.cityField, address.city);
    }

    if (address.province && await this.isElementVisible(this.provinceSelect)) {
      await this.selectDropdown(this.provinceSelect, address.province);
    }

    if (address.postalCode) {
      await this.typeText(this.postalCodeField, address.postalCode);
    }

    if (address.country && await this.isElementVisible(this.countrySelect)) {
      await this.selectDropdown(this.countrySelect, address.country);
    }

    this.logger.info('Address information filled successfully');
  }

  /**
   * Fill employment information section
   */
  async fillEmploymentInformation(employment: any): Promise<void> {
    this.logger.action('Filling employment information section');

    if (employment.status) {
      await this.selectDropdown(this.employmentStatusSelect, employment.status);
    }

    if (employment.lastEmployer && await this.isElementVisible(this.lastEmployerField)) {
      await this.typeText(this.lastEmployerField, employment.lastEmployer);
    }

    if (employment.jobTitle && await this.isElementVisible(this.jobTitleField)) {
      await this.typeText(this.jobTitleField, employment.jobTitle);
    }

    if (employment.industry && await this.isElementVisible(this.industrySelect)) {
      await this.selectDropdown(this.industrySelect, employment.industry);
    }

    if (employment.lastWorkDate && await this.isElementVisible(this.lastWorkDateField)) {
      await this.typeText(this.lastWorkDateField, employment.lastWorkDate);
    }

    this.logger.info('Employment information filled successfully');
  }

  /**
   * Navigate to next step
   */
  async clickNext(): Promise<void> {
    this.logger.action('Clicking Next button');
    await this.clickElement(this.nextButton);
    await this.waitForPageLoad();
  }

  /**
   * Navigate to previous step
   */
  async clickPrevious(): Promise<void> {
    this.logger.action('Clicking Previous button');
    await this.clickElement(this.previousButton);
    await this.waitForPageLoad();
  }

  /**
   * Save current progress
   */
  async clickSave(): Promise<void> {
    this.logger.action('Clicking Save button');
    await this.clickElement(this.saveButton);
    await this.waitForLoadingToComplete();
  }

  /**
   * Submit application
   */
  async clickSubmit(): Promise<void> {
    this.logger.action('Clicking Submit button');
    await this.clickElement(this.submitButton);
    await this.waitForLoadingToComplete();
  }

  /**
   * Get current step number
   */
  async getCurrentStep(): Promise<string> {
    if (await this.isElementVisible(this.currentStep)) {
      return await this.getElementText(this.currentStep);
    }
    return '';
  }

  /**
   * Verify required fields are marked
   */
  async verifyRequiredFields(): Promise<void> {
    this.logger.assertion('Verifying required fields are marked');
    
    const requiredFields = [
      this.firstNameField,
      this.lastNameField,
      this.sinField,
      this.phoneField,
      this.emailField
    ];

    for (const field of requiredFields) {
      if (await this.isElementVisible(field)) {
        const fieldElement = this.page.locator(field);
        const isRequired = await fieldElement.getAttribute('required') !== null ||
                          await fieldElement.getAttribute('aria-required') === 'true';
        
        if (!isRequired) {
          // Check for visual indicators like asterisk
          const parentElement = fieldElement.locator('..');
          const hasAsterisk = await parentElement.locator('text=*').count() > 0;
          expect(hasAsterisk).toBeTruthy();
        }
      }
    }

    this.logger.info('Required fields verification completed');
  }

  /**
   * Verify page is loaded
   */
  async verifyPageLoaded(): Promise<void> {
    this.logger.assertion('Verifying ESI application page is loaded');
    
    await this.waitForPageLoad();
    
    // Check for form sections or key elements
    const pageLoaded = await this.isElementVisible(this.personalInfoSection) ||
                      await this.isElementVisible(this.firstNameField) ||
                      await this.isElementVisible(this.progressBar);
    
    expect(pageLoaded).toBeTruthy();
    this.logger.info('ESI application page loaded successfully');
  }

  /**
   * Fill complete ESI application
   */
  async fillCompleteApplication(applicationData: any): Promise<void> {
    this.logger.action('Filling complete ESI application');

    // Fill personal information
    if (applicationData.personalInfo) {
      await this.fillPersonalInformation(applicationData.personalInfo);
    }

    // Fill address information
    if (applicationData.address) {
      await this.fillAddressInformation(applicationData.address);
    }

    // Fill employment information
    if (applicationData.employment) {
      await this.fillEmploymentInformation(applicationData.employment);
    }

    this.logger.info('Complete ESI application filled successfully');
  }
}
