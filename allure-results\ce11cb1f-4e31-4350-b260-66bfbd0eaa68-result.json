{"uuid": "ce11cb1f-4e31-4350-b260-66bfbd0eaa68", "name": "should handle page refresh correctly @regression", "historyId": "65ede488e88d5e46930bd195693a89fe:f0494070a4792d94c64308aac1604abb", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Safari"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "home.test.ts"}, {"name": "titlePath", "value": " > Mobile Safari > home.test.ts > RASP Home Page Tests - Basic"}, {"name": "tag", "value": "regression"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-36364-worker-0"}, {"name": "parentSuite", "value": "Mobile Safari"}, {"name": "subSuite", "value": "RASP Home Page Tests - Basic"}], "links": [], "start": 1761577068559, "testCaseId": "65ede488e88d5e46930bd195693a89fe", "fullName": "home.test.ts:135:7", "titlePath": ["home.test.ts", "RASP Home Page Tests - Basic"], "stop": 1761577068559}