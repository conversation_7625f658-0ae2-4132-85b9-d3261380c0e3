{"uuid": "4c1d4fec-78d5-4fbb-8671-f2991eb090e5", "name": "should handle browser back navigation @regression", "historyId": "2d16f64fb5e901f85d4ca02bdf65c012:4151609b82fd60da52fe302128be4a3c", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Google Chrome"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "home.test.ts"}, {"name": "titlePath", "value": " > Google Chrome > home.test.ts > RASP Home Page Tests - Basic"}, {"name": "tag", "value": "regression"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-36364-worker-0"}, {"name": "parentSuite", "value": "Google Chrome"}, {"name": "subSuite", "value": "RASP Home Page Tests - Basic"}], "links": [], "start": 1761577068579, "testCaseId": "2d16f64fb5e901f85d4ca02bdf65c012", "fullName": "home.test.ts:152:7", "titlePath": ["home.test.ts", "RASP Home Page Tests - Basic"], "stop": 1761577068579}