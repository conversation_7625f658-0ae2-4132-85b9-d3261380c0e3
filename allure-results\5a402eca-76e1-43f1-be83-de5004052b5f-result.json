{"uuid": "5a402eca-76e1-43f1-be83-de5004052b5f", "name": "should fill employment information for employed applicant @smoke", "historyId": "3684773979b995ace91c6963ffaac888:2d9eadd8e5698e677c4123f9b6b8cac6", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Microsoft Edge"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "eap-application.test.ts"}, {"name": "titlePath", "value": " > Microsoft Edge > eap-application.test.ts > EAP Application Tests"}, {"name": "tag", "value": "smoke"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-49836-worker-0"}, {"name": "parentSuite", "value": "Microsoft Edge"}, {"name": "subSuite", "value": "EAP Application Tests"}], "links": [], "start": 1761576670074, "testCaseId": "3684773979b995ace91c6963ffaac888", "fullName": "eap-application.test.ts:62:7", "titlePath": ["eap-application.test.ts", "EAP Application Tests"], "stop": 1761576670074}