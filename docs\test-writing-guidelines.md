# Test Writing Guidelines

This document provides comprehensive guidelines for writing effective and maintainable tests using the RASP Playwright framework.

## Test Structure and Organization

### Test File Naming

Follow these naming conventions:

```
src/tests/
├── home.test.ts                    # Home page functionality
├── esi-application.test.ts         # ESI application tests
├── eap-application.test.ts         # EAP application tests
├── navigation.test.ts              # Navigation-specific tests
└── integration/                    # Integration tests
    ├── esi-end-to-end.test.ts
    └── eap-end-to-end.test.ts
```

### Test Suite Structure

```typescript
import { test, expect } from '@playwright/test';
import { HomePage } from '../pages/home.page';
import { Logger } from '../utils/logger';

test.describe('Feature Name Tests', () => {
  let homePage: HomePage;
  let logger: Logger;

  test.beforeEach(async ({ page }) => {
    logger = Logger.getInstance();
    homePage = new HomePage(page);
    
    // Setup steps
    await homePage.navigateToHome();
    await homePage.verifyPageLoaded();
  });

  test.afterEach(async ({ page }, testInfo) => {
    // Cleanup and reporting
    const status = testInfo.status === 'passed' ? 'PASSED' : 'FAILED';
    logger.testEnd(testInfo.title, status);
    
    if (testInfo.status === 'failed') {
      await homePage.takeScreenshot(`failed-${testInfo.title.replace(/\s+/g, '-')}`);
    }
  });

  test('should perform specific action @smoke', async () => {
    // Test implementation
  });
});
```

## Test Categories and Tags

Use tags to categorize tests for selective execution:

### Primary Tags
- `@smoke`: Critical functionality that must work
- `@regression`: Comprehensive test coverage
- `@negative`: Error handling and validation tests
- `@integration`: End-to-end workflow tests

### Secondary Tags
- `@esi`: ESI-specific tests
- `@eap`: EAP-specific tests
- `@forms`: Form-related tests
- `@navigation`: Navigation tests
- `@accessibility`: Accessibility tests

### Example Usage
```typescript
test('should complete ESI application successfully @smoke @esi @forms', async () => {
  // Test implementation
});

test('should handle invalid data gracefully @negative @forms', async () => {
  // Test implementation
});
```

## Page Object Model Best Practices

### Page Class Structure

```typescript
import { Page, expect } from '@playwright/test';
import { BasePage } from './base.page';

export class FeaturePage extends BasePage {
  // Selectors - use data-testid when possible
  private readonly submitButton = '[data-testid="submit-button"]';
  private readonly nameField = 'input[name="firstName"]';
  private readonly errorMessage = '.error-message';

  constructor(page: Page) {
    super(page);
  }

  // Action methods
  async fillForm(data: FormData): Promise<void> {
    this.logger.action('Filling form with provided data');
    
    await this.typeText(this.nameField, data.firstName);
    // ... other form fields
    
    this.logger.info('Form filled successfully');
  }

  async submitForm(): Promise<void> {
    this.logger.action('Submitting form');
    await this.clickElement(this.submitButton);
    await this.waitForLoadingToComplete();
  }

  // Verification methods
  async verifyFormSubmitted(): Promise<void> {
    this.logger.assertion('Verifying form submission');
    
    const hasError = await this.hasErrorMessage();
    expect(hasError).toBeFalsy();
    
    this.logger.info('Form submission verified');
  }

  // Required implementation from BasePage
  async verifyPageLoaded(): Promise<void> {
    await this.waitForPageLoad();
    const isVisible = await this.isElementVisible(this.submitButton);
    expect(isVisible).toBeTruthy();
  }
}
```

### Selector Best Practices

1. **Priority Order**:
   ```typescript
   // 1. Data test IDs (preferred)
   '[data-testid="submit-button"]'
   
   // 2. Semantic attributes
   'button[type="submit"]'
   
   // 3. Form names
   'input[name="firstName"]'
   
   // 4. CSS classes (stable ones only)
   '.submit-btn'
   
   // 5. Text content (last resort)
   'button:has-text("Submit")'
   ```

2. **Avoid**:
   - Complex CSS selectors
   - XPath expressions
   - Positional selectors (nth-child)
   - Styling-based selectors

## Test Data Management

### Using DataManager

```typescript
import { DataManager } from '../utils/data-manager';

test('should complete application with test data', async () => {
  const dataManager = new DataManager();
  
  // Load predefined data
  const testData = dataManager.getFormData('ESI', 'default');
  
  // Generate random data
  const randomData = dataManager.generateRandomUserData();
  
  // Use environment-specific data
  const envData = dataManager.getEnvironmentData('qa');
  
  await esiPage.fillCompleteApplication(testData);
});
```

### Test Data Structure

```typescript
// src/data/esi-forms.json
{
  "default": {
    "personalInfo": {
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>"
    },
    "address": {
      "street": "123 Main St",
      "city": "Toronto"
    }
  },
  "minimal": {
    // Minimal required data
  },
  "complete": {
    // All possible fields
  }
}
```

## Assertions and Verifications

### Assertion Patterns

```typescript
// Basic assertions
expect(actualValue).toBe(expectedValue);
expect(actualValue).toBeTruthy();
expect(actualValue).toContain(substring);

// Page-specific assertions
await expect(page.locator(selector)).toBeVisible();
await expect(page.locator(selector)).toHaveText(expectedText);
await expect(page).toHaveURL(/expected-url-pattern/);

// Custom assertions with logging
async verifyApplicationSubmitted(): Promise<void> {
  this.logger.assertion('Verifying application submission');
  
  const successMessage = await this.getSuccessMessage();
  expect(successMessage).toContain('Application submitted successfully');
  
  const currentUrl = await this.getCurrentUrl();
  expect(currentUrl).toContain('confirmation');
  
  this.logger.info('Application submission verified');
}
```

### Error Handling

```typescript
test('should handle validation errors @negative', async () => {
  // Submit form with invalid data
  await formPage.submitFormWithInvalidData();
  
  // Verify error handling
  const hasError = await formPage.hasErrorMessage();
  expect(hasError).toBeTruthy();
  
  const errorMessage = await formPage.getErrorMessage();
  expect(errorMessage).toContain('required field');
  
  // Verify form doesn't advance
  await formPage.verifyPageLoaded(); // Still on same page
});
```

## Logging and Debugging

### Logging Best Practices

```typescript
test('should complete workflow', async () => {
  logger.testStart('Complete workflow test');
  
  logger.step('Navigate to application page');
  await homePage.navigateToESI();
  
  logger.action('Filling personal information');
  await esiPage.fillPersonalInformation(testData.personalInfo);
  
  logger.assertion('Verifying form submission');
  await esiPage.verifyFormSubmitted();
  
  logger.info('Workflow completed successfully');
});
```

### Debug-Friendly Tests

```typescript
test('should debug failing scenario', async ({ page }) => {
  // Add debug points
  await page.pause(); // Pauses execution for manual inspection
  
  // Take screenshots at key points
  await esiPage.takeScreenshot('before-form-submission');
  
  // Log intermediate states
  const formData = await page.evaluate(() => {
    return Array.from(document.forms[0].elements)
      .map(el => ({ name: el.name, value: el.value }));
  });
  logger.debug('Form data before submission', formData);
});
```

## Performance and Reliability

### Waiting Strategies

```typescript
// Wait for elements
await page.waitForSelector(selector, { state: 'visible' });

// Wait for network
await page.waitForLoadState('networkidle');

// Wait for custom conditions
await page.waitForFunction(() => {
  return document.querySelector('.loading') === null;
});

// Use framework helpers
await this.helpers.waitForElement(page, selector);
await this.helpers.waitForPageLoad(page);
```

### Retry Mechanisms

```typescript
// Built into framework helpers
await this.helpers.safeClick(page, selector, 3); // 3 retries
await this.helpers.safeType(page, selector, text, 3);

// Custom retry logic
async function retryAction(action: () => Promise<void>, maxRetries: number = 3): Promise<void> {
  for (let i = 0; i < maxRetries; i++) {
    try {
      await action();
      return;
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await page.waitForTimeout(1000);
    }
  }
}
```

## Test Maintenance

### Keeping Tests Stable

1. **Use stable selectors**: Prefer data-testid over CSS classes
2. **Implement proper waits**: Don't use fixed timeouts
3. **Handle dynamic content**: Wait for specific states
4. **Isolate tests**: Each test should be independent
5. **Clean up**: Reset state between tests

### Regular Maintenance Tasks

```typescript
// Update selectors when UI changes
private readonly submitButton = '[data-testid="submit-button"], .btn-submit, button[type="submit"]';

// Version test data
const testData = dataManager.getFormData('ESI', 'v2.0');

// Update assertions for new requirements
expect(successMessage).toMatch(/Application (submitted|received) successfully/);
```

## Code Quality Standards

### TypeScript Best Practices

```typescript
// Use proper types
interface FormData {
  personalInfo: PersonalInfo;
  address: Address;
  employment: Employment;
}

// Avoid any type
const data: FormData = await loadTestData();

// Use async/await consistently
async fillForm(data: FormData): Promise<void> {
  await this.typeText(this.nameField, data.personalInfo.firstName);
}
```

### ESLint and Prettier

The framework enforces code quality through:
- ESLint for code quality
- Prettier for formatting
- TypeScript strict mode

Run quality checks:
```bash
npm run lint
npm run format
npm run build
```

## Common Patterns and Examples

### Form Testing Pattern

```typescript
test('should complete form workflow @smoke', async () => {
  const testData = dataManager.getFormData('ESI', 'default');
  
  // Fill form sections
  await esiPage.fillPersonalInformation(testData.personalInfo);
  await esiPage.fillAddressInformation(testData.address);
  await esiPage.fillEmploymentInformation(testData.employment);
  
  // Submit and verify
  await esiPage.clickSubmit();
  await esiPage.verifyFormSubmitted();
});
```

### Navigation Testing Pattern

```typescript
test('should navigate between sections @regression', async () => {
  // Fill first section
  await esiPage.fillPersonalInformation(testData.personalInfo);
  
  // Navigate forward
  await esiPage.clickNext();
  await esiPage.verifyPageLoaded();
  
  // Navigate backward
  await esiPage.clickPrevious();
  await esiPage.verifyPageLoaded();
  
  // Verify data persistence
  const firstName = await esiPage.getFieldValue(esiPage.firstNameField);
  expect(firstName).toBe(testData.personalInfo.firstName);
});
```

### Error Handling Pattern

```typescript
test('should validate required fields @negative', async () => {
  // Submit without required data
  await esiPage.clickSubmit();
  
  // Verify validation
  const hasError = await esiPage.hasErrorMessage();
  expect(hasError).toBeTruthy();
  
  const errorMessage = await esiPage.getErrorMessage();
  expect(errorMessage).toContain('required');
  
  // Verify form doesn't advance
  await esiPage.verifyPageLoaded();
});
```

Following these guidelines will help you create maintainable, reliable, and effective tests for the RASP application.
