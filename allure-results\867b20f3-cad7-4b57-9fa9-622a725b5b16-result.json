{"uuid": "867b20f3-cad7-4b57-9fa9-622a725b5b16", "name": "should handle invalid program codes gracefully @negative", "historyId": "bf552e89616dfe6bfa6098d4b87bc6fc:96797789f4669ba5e9de53cebde9126b", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "Mobile Chrome"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "home.test.ts"}, {"name": "titlePath", "value": " > Mobile Chrome > home.test.ts > RASP Home Page Tests - Basic"}, {"name": "tag", "value": "negative"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-36364-worker-0"}, {"name": "parentSuite", "value": "Mobile Chrome"}, {"name": "subSuite", "value": "RASP Home Page Tests - Basic"}], "links": [], "start": 1761577068552, "testCaseId": "bf552e89616dfe6bfa6098d4b87bc6fc", "fullName": "home.test.ts:183:7", "titlePath": ["home.test.ts", "RASP Home Page Tests - Basic"], "stop": 1761577068552}