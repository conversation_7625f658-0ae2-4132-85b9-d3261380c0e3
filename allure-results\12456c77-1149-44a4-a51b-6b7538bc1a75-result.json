{"uuid": "12456c77-1149-44a4-a51b-6b7538bc1a75", "name": "should display welcome message", "historyId": "636af9f23ac4d8bea4e6db7d0ec9cba1:84e28e814b821ed013329cc8dbc467e0", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "webkit"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "home.test.ts"}, {"name": "titlePath", "value": " > webkit > home.test.ts > RASP Home Page Tests - Basic"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-36364-worker-0"}, {"name": "parentSuite", "value": "webkit"}, {"name": "subSuite", "value": "RASP Home Page Tests - Basic"}], "links": [], "start": 1761577068534, "testCaseId": "636af9f23ac4d8bea4e6db7d0ec9cba1", "fullName": "home.test.ts:102:7", "titlePath": ["home.test.ts", "RASP Home Page Tests - Basic"], "stop": 1761577068534}