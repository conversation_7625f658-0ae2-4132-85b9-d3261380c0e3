import { test, expect } from '@playwright/test';
import { HomePage } from '../pages/home.page';
import { ESIApplicationPage } from '../pages/esi-application.page';
import { DataManager } from '../utils/data-manager';
import { Logger } from '../utils/logger';

test.describe('ESI Application Tests', () => {
  let homePage: HomePage;
  let esiPage: ESIApplicationPage;
  let dataManager: DataManager;
  let logger: Logger;

  test.beforeEach(async ({ page }) => {
    logger = Logger.getInstance();
    homePage = new HomePage(page);
    esiPage = new ESIApplicationPage(page);
    dataManager = new DataManager();
    
    logger.testStart('ESI application setup');
    await homePage.navigateToESI();
    await esiPage.verifyPageLoaded();
  });

  test.afterEach(async ({ page }, testInfo) => {
    const status = testInfo.status === 'passed' ? 'PASSED' : 'FAILED';
    logger.testEnd(testInfo.title, status);
    
    if (testInfo.status === 'failed') {
      await esiPage.takeScreenshot(`failed-esi-${testInfo.title.replace(/\s+/g, '-')}`);
    }
  });

  test('should load ESI application form @smoke', async () => {
    logger.testStart('ESI application form load test');
    
    // Verify page is loaded
    await esiPage.verifyPageLoaded();
    
    // Verify page title contains ESI
    const pageTitle = await esiPage.getPageTitle();
    expect(pageTitle.toLowerCase()).toContain('esi');
    logger.assertion(`ESI page title verified: ${pageTitle}`);
    
    // Verify URL contains ESI
    const currentUrl = await esiPage.getCurrentUrl();
    expect(currentUrl).toContain('ESI');
    logger.assertion('ESI URL verified');
  });

  test('should fill personal information with valid data @smoke', async () => {
    logger.testStart('ESI personal information test');
    
    const testData = dataManager.getFormData('ESI', 'default');
    await esiPage.fillPersonalInformation(testData.personalInfo);
    
    // Verify no error messages
    const hasError = await esiPage.hasErrorMessage();
    expect(hasError).toBeFalsy();
    logger.assertion('Personal information filled without errors');
  });

  test('should fill address information with valid data @smoke', async () => {
    logger.testStart('ESI address information test');
    
    const testData = dataManager.getFormData('ESI', 'default');
    await esiPage.fillAddressInformation(testData.address);
    
    // Verify no error messages
    const hasError = await esiPage.hasErrorMessage();
    expect(hasError).toBeFalsy();
    logger.assertion('Address information filled without errors');
  });

  test('should fill employment information with valid data @smoke', async () => {
    logger.testStart('ESI employment information test');
    
    const testData = dataManager.getFormData('ESI', 'default');
    await esiPage.fillEmploymentInformation(testData.employment);
    
    // Verify no error messages
    const hasError = await esiPage.hasErrorMessage();
    expect(hasError).toBeFalsy();
    logger.assertion('Employment information filled without errors');
  });

  test('should complete full ESI application with default data @regression', async () => {
    logger.testStart('Complete ESI application test');
    
    const testData = dataManager.getFormData('ESI', 'default');
    await esiPage.fillCompleteApplication(testData);
    
    // Try to save the application
    await esiPage.clickSave();
    
    // Verify success or no errors
    const hasError = await esiPage.hasErrorMessage();
    const hasSuccess = await esiPage.hasSuccessMessage();
    
    if (hasSuccess) {
      const successMessage = await esiPage.getSuccessMessage();
      logger.assertion(`Application saved successfully: ${successMessage}`);
    } else {
      expect(hasError).toBeFalsy();
      logger.assertion('Application saved without errors');
    }
  });

  test('should complete full ESI application with minimal data @regression', async () => {
    logger.testStart('ESI minimal data application test');
    
    const testData = dataManager.getFormData('ESI', 'minimal');
    await esiPage.fillCompleteApplication(testData);
    
    // Try to save the application
    await esiPage.clickSave();
    
    // Verify success or no errors
    const hasError = await esiPage.hasErrorMessage();
    expect(hasError).toBeFalsy();
    logger.assertion('Minimal application data accepted');
  });

  test('should complete full ESI application with complete data @regression', async () => {
    logger.testStart('ESI complete data application test');
    
    const testData = dataManager.getFormData('ESI', 'complete');
    await esiPage.fillCompleteApplication(testData);
    
    // Try to save the application
    await esiPage.clickSave();
    
    // Verify success
    const hasError = await esiPage.hasErrorMessage();
    expect(hasError).toBeFalsy();
    logger.assertion('Complete application data accepted');
  });

  test('should validate required fields @negative', async () => {
    logger.testStart('ESI required fields validation test');
    
    // Try to submit without filling required fields
    await esiPage.clickNext();
    
    // Should show validation errors
    const hasError = await esiPage.hasErrorMessage();
    if (hasError) {
      const errorMessage = await esiPage.getErrorMessage();
      logger.assertion(`Validation error displayed: ${errorMessage}`);
    } else {
      // Check if we're still on the same page (didn't advance)
      await esiPage.verifyPageLoaded();
      logger.assertion('Form did not advance without required fields');
    }
  });

  test('should handle invalid SIN format @negative', async () => {
    logger.testStart('Invalid SIN format test');
    
    const invalidData = {
      personalInfo: {
        firstName: 'Test',
        lastName: 'User',
        sin: '123', // Invalid SIN format
        phone: '4165551234',
        email: '<EMAIL>'
      }
    };
    
    await esiPage.fillPersonalInformation(invalidData.personalInfo);
    await esiPage.clickNext();
    
    // Should show validation error for SIN
    const hasError = await esiPage.hasErrorMessage();
    if (hasError) {
      const errorMessage = await esiPage.getErrorMessage();
      expect(errorMessage.toLowerCase()).toContain('sin');
      logger.assertion(`SIN validation error: ${errorMessage}`);
    }
  });

  test('should handle invalid email format @negative', async () => {
    logger.testStart('Invalid email format test');
    
    const invalidData = {
      personalInfo: {
        firstName: 'Test',
        lastName: 'User',
        sin: '123456789',
        phone: '4165551234',
        email: 'invalid-email' // Invalid email format
      }
    };
    
    await esiPage.fillPersonalInformation(invalidData.personalInfo);
    await esiPage.clickNext();
    
    // Should show validation error for email
    const hasError = await esiPage.hasErrorMessage();
    if (hasError) {
      const errorMessage = await esiPage.getErrorMessage();
      expect(errorMessage.toLowerCase()).toContain('email');
      logger.assertion(`Email validation error: ${errorMessage}`);
    }
  });

  test('should handle invalid phone format @negative', async () => {
    logger.testStart('Invalid phone format test');
    
    const invalidData = {
      personalInfo: {
        firstName: 'Test',
        lastName: 'User',
        sin: '123456789',
        phone: '123', // Invalid phone format
        email: '<EMAIL>'
      }
    };
    
    await esiPage.fillPersonalInformation(invalidData.personalInfo);
    await esiPage.clickNext();
    
    // Should show validation error for phone
    const hasError = await esiPage.hasErrorMessage();
    if (hasError) {
      const errorMessage = await esiPage.getErrorMessage();
      expect(errorMessage.toLowerCase()).toContain('phone');
      logger.assertion(`Phone validation error: ${errorMessage}`);
    }
  });

  test('should navigate between form steps @regression', async () => {
    logger.testStart('ESI form navigation test');
    
    // Fill some basic info
    const testData = dataManager.getFormData('ESI', 'minimal');
    await esiPage.fillPersonalInformation(testData.personalInfo);
    
    // Try to go to next step
    await esiPage.clickNext();
    
    // Verify we can go back
    await esiPage.clickPrevious();
    
    // Verify we're back to the form
    await esiPage.verifyPageLoaded();
    logger.assertion('Form navigation working correctly');
  });

  test('should save application progress @regression', async () => {
    logger.testStart('ESI save progress test');
    
    const testData = dataManager.getFormData('ESI', 'default');
    await esiPage.fillPersonalInformation(testData.personalInfo);
    
    // Save progress
    await esiPage.clickSave();
    
    // Verify save was successful
    const hasError = await esiPage.hasErrorMessage();
    const hasSuccess = await esiPage.hasSuccessMessage();
    
    expect(hasError).toBeFalsy();
    if (hasSuccess) {
      const successMessage = await esiPage.getSuccessMessage();
      logger.assertion(`Progress saved: ${successMessage}`);
    } else {
      logger.assertion('Progress saved without errors');
    }
  });

  test('should generate random test data and fill form @regression', async () => {
    logger.testStart('Random test data generation test');
    
    const randomData = dataManager.generateRandomUserData();
    
    const applicationData = {
      personalInfo: {
        firstName: randomData.firstName,
        lastName: randomData.lastName,
        dateOfBirth: randomData.dateOfBirth,
        sin: randomData.sin,
        phone: randomData.phone,
        email: randomData.email
      },
      address: randomData.address,
      employment: randomData.employment
    };
    
    await esiPage.fillCompleteApplication(applicationData);
    await esiPage.clickSave();
    
    const hasError = await esiPage.hasErrorMessage();
    expect(hasError).toBeFalsy();
    logger.assertion('Random test data application completed successfully');
  });
});
