{"uuid": "e4cc2f06-d74b-4f92-aa5e-d04253c8e2eb", "name": "should display footer information", "historyId": "3742aab49d1a83d263641b8e6a4ecc9d:84e28e814b821ed013329cc8dbc467e0", "status": "skipped", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "Project", "value": "webkit"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "home.test.ts"}, {"name": "titlePath", "value": " > webkit > home.test.ts > RASP Home Page Tests - Basic"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-36364-worker-0"}, {"name": "parentSuite", "value": "webkit"}, {"name": "subSuite", "value": "RASP Home Page Tests - Basic"}], "links": [], "start": 1761577068536, "testCaseId": "3742aab49d1a83d263641b8e6a4ecc9d", "fullName": "home.test.ts:127:7", "titlePath": ["home.test.ts", "RASP Home Page Tests - Basic"], "stop": 1761577068536}