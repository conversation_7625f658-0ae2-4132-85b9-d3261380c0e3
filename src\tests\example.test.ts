import { test, expect } from '@playwright/test';

test.describe('Example Tests - Basic Verification', () => {
  
  test('should verify <PERSON><PERSON> is working', async ({ page }) => {
    console.log('🧪 Running basic Playwright verification test...');
    
    // Navigate to a simple page to test basic functionality
    await page.goto('https://example.com');
    await page.waitForLoadState('networkidle');
    
    // Verify page title
    const title = await page.title();
    expect(title).toBeTruthy();
    console.log(`✅ Page title: ${title}`);
    
    // Verify page content
    const heading = await page.locator('h1').first();
    await expect(heading).toBeVisible();
    
    const headingText = await heading.textContent();
    console.log(`✅ Page heading: ${headingText}`);
    
    console.log('🎉 Basic Playwright test completed successfully!');
  });

  test('should test RASP application accessibility', async ({ page }) => {
    console.log('🧪 Testing RASP application basic accessibility...');
    
    try {
      // Navigate to RASP application
      const raspUrl = 'https://qa4.employmentontario.labour.gov.on.ca/faec/?lang=en&programCode=ESI';
      console.log(`🌐 Navigating to: ${raspUrl}`);
      
      await page.goto(raspUrl, { timeout: 30000 });
      await page.waitForLoadState('networkidle', { timeout: 30000 });
      
      // Verify page loaded
      const title = await page.title();
      console.log(`✅ RASP page title: ${title}`);
      
      // Check if page has content
      const bodyText = await page.textContent('body');
      expect(bodyText).toBeTruthy();
      expect(bodyText.length).toBeGreaterThan(100);
      console.log(`✅ Page content loaded (${bodyText.length} characters)`);
      
      // Look for common form elements
      const forms = await page.locator('form').count();
      console.log(`📝 Found ${forms} form(s) on the page`);
      
      // Look for input fields
      const inputs = await page.locator('input').count();
      console.log(`📝 Found ${inputs} input field(s) on the page`);
      
      // Look for buttons
      const buttons = await page.locator('button, input[type="button"], input[type="submit"]').count();
      console.log(`🔘 Found ${buttons} button(s) on the page`);
      
      console.log('🎉 RASP application accessibility test completed!');
      
    } catch (error) {
      console.error('❌ Error accessing RASP application:', error.message);
      
      // Take a screenshot for debugging
      await page.screenshot({ 
        path: 'screenshots/rasp-error.png', 
        fullPage: true 
      });
      console.log('📸 Screenshot saved to screenshots/rasp-error.png');
      
      // Still fail the test but with more info
      throw new Error(`RASP application test failed: ${error.message}`);
    }
  });

  test('should verify browser capabilities', async ({ page, browserName }) => {
    console.log(`🧪 Testing browser capabilities for: ${browserName}`);
    
    // Test JavaScript execution
    const userAgent = await page.evaluate(() => navigator.userAgent);
    console.log(`🌐 User Agent: ${userAgent}`);
    
    // Test viewport
    const viewport = page.viewportSize();
    console.log(`📱 Viewport: ${viewport?.width}x${viewport?.height}`);
    
    // Test local storage
    await page.evaluate(() => {
      localStorage.setItem('test', 'playwright-working');
    });
    
    const stored = await page.evaluate(() => {
      return localStorage.getItem('test');
    });
    
    expect(stored).toBe('playwright-working');
    console.log('✅ Local storage working');
    
    // Test cookies
    await page.context().addCookies([{
      name: 'test-cookie',
      value: 'playwright-test',
      domain: 'example.com',
      path: '/'
    }]);
    
    await page.goto('https://example.com');
    const cookies = await page.context().cookies();
    const testCookie = cookies.find(c => c.name === 'test-cookie');
    expect(testCookie).toBeTruthy();
    console.log('✅ Cookies working');
    
    console.log('🎉 Browser capabilities test completed!');
  });

  test('should test network and proxy configuration', async ({ page }) => {
    console.log('🧪 Testing network and proxy configuration...');
    
    // Test basic internet connectivity
    try {
      await page.goto('https://httpbin.org/ip', { timeout: 15000 });
      const response = await page.textContent('pre');
      console.log(`🌐 Network response: ${response}`);
      
      // Parse IP information
      if (response) {
        const ipInfo = JSON.parse(response);
        console.log(`📍 External IP: ${ipInfo.origin}`);
      }
      
    } catch (error) {
      console.log(`⚠️ Network test failed (expected in corporate environment): ${error.message}`);
    }
    
    // Test if we can reach the RASP domain
    try {
      const response = await page.goto('https://qa4.employmentontario.labour.gov.on.ca', { 
        timeout: 20000,
        waitUntil: 'domcontentloaded'
      });
      
      const status = response?.status();
      console.log(`🏛️ RASP domain status: ${status}`);
      
      if (status && status < 400) {
        console.log('✅ RASP domain is accessible');
      } else {
        console.log('⚠️ RASP domain returned error status');
      }
      
    } catch (error) {
      console.log(`❌ Cannot reach RASP domain: ${error.message}`);
      throw error;
    }
    
    console.log('🎉 Network configuration test completed!');
  });
});
