{"uuid": "a94345c0-f078-4fe8-88e6-38b917eebc48", "name": "should load EAP application form @smoke", "historyId": "e9d8af61ba8124fb85c5e869b230719a:5bd835b0d6b1d4ada3b9f0db936e82c8", "status": "failed", "statusDetails": {"message": "TypeError: Cannot read properties of null (reading 'newContext')", "trace": "TypeError: Cannot read properties of null (reading 'newContext')"}, "stage": "finished", "steps": [{"statusDetails": {}, "stage": "running", "steps": [{"status": "failed", "statusDetails": {"message": "Fixture \"browser\" timeout of 0ms exceeded during setup.", "trace": "Fixture \"browser\" timeout of 0ms exceeded during setup."}, "stage": "finished", "steps": [{"status": "failed", "statusDetails": {"message": "Fixture \"browser\" timeout of 0ms exceeded during setup.", "trace": "Fixture \"browser\" timeout of 0ms exceeded during setup."}, "stage": "finished", "steps": [{"status": "failed", "statusDetails": {"message": "Fixture \"browser\" timeout of 0ms exceeded during setup.", "trace": "Fixture \"browser\" timeout of 0ms exceeded during setup."}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "name": "Launch browser", "start": 1761576542519, "uuid": "b882c37f-c790-4f17-a1fe-19226be574e4", "stop": 1761576609490}], "attachments": [], "parameters": [], "name": "Fixture \"browser\"", "start": 1761576542516, "uuid": "faab1a88-9b96-4419-8c92-8654d3daa5cf", "stop": 1761576609497}], "attachments": [], "parameters": [], "name": "beforeEach hook", "start": 1761576542506, "uuid": "aa68476d-8360-4a1f-a090-a521ba1af16c", "stop": 1761576609487}], "attachments": [], "parameters": [], "name": "Before Hooks", "start": 1761576542506, "uuid": "96b286f4-8baf-46aa-b909-c43fcde72730"}, {"status": "failed", "statusDetails": {"message": "TypeError: Cannot read properties of null (reading 'newContext')", "trace": "TypeError: Cannot read properties of null (reading 'newContext')"}, "stage": "finished", "steps": [{"status": "failed", "statusDetails": {"message": "TypeError: Cannot read properties of null (reading 'newContext')", "trace": "TypeError: Cannot read properties of null (reading 'newContext')"}, "stage": "finished", "steps": [{"status": "failed", "statusDetails": {"message": "TypeError: Cannot read properties of null (reading 'newContext')", "trace": "TypeError: Cannot read properties of null (reading 'newContext')"}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "name": "Fixture \"context\"", "start": 1761576609490, "uuid": "76556d43-2340-47d6-98d1-21d6b1d9ca43", "stop": 1761576609492}], "attachments": [], "parameters": [], "name": "after<PERSON>ach hook", "start": 1761576609489, "uuid": "e95af369-bb1e-4247-addc-889df9096538", "stop": 1761576609492}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "name": "Fixture \"context\"", "start": 1761576609493, "uuid": "a5e38a88-42ca-4026-81f3-803c5fbf2451", "stop": 1761576609493}], "attachments": [], "parameters": [], "name": "After Hooks", "start": 1761576609488, "uuid": "3c938a2b-4316-468c-924c-18fb1cb901c5", "stop": 1761576609502}], "attachments": [], "parameters": [{"name": "Project", "value": "chromium"}], "labels": [{"name": "language", "value": "javascript"}, {"name": "framework", "value": "playwright"}, {"name": "package", "value": "eap-application.test.ts"}, {"name": "titlePath", "value": " > chromium > eap-application.test.ts > EAP Application Tests"}, {"name": "tag", "value": "smoke"}, {"name": "host", "value": "OPS-PF5E5A4G"}, {"name": "thread", "value": "pid-49836-worker-0"}, {"name": "parentSuite", "value": "chromium"}, {"name": "subSuite", "value": "EAP Application Tests"}], "links": [], "start": 1761576542509, "testCaseId": "e9d8af61ba8124fb85c5e869b230719a", "fullName": "eap-application.test.ts:33:7", "titlePath": ["eap-application.test.ts", "EAP Application Tests"], "stop": 1761576542514}