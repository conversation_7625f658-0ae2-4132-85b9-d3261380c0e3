# Troubleshooting Guide

This guide helps you resolve common issues when using the RASP Playwright TypeScript framework.

## Installation Issues

### Node.js and npm Issues

#### Problem: "node: command not found"
**Solution:**
1. Install Node.js from [nodejs.org](https://nodejs.org/)
2. Restart your terminal
3. Verify installation: `node --version`

#### Problem: "npm: command not found"
**Solution:**
1. Node.js installation should include npm
2. If missing, reinstall Node.js
3. Verify: `npm --version`

#### Problem: Node.js version too old
**Solution:**
```bash
# Check current version
node --version

# Update Node.js to latest LTS version
# Download from nodejs.org or use version manager
```

### Playwright Installation Issues

#### Problem: "playwright: command not found"
**Solution:**
```bash
# Install Playwright
npm install @playwright/test

# Install browsers
npx playwright install
```

#### Problem: Browser installation fails
**Solution:**
```bash
# Clear cache and reinstall
npm cache clean --force
rm -rf node_modules
npm install
npx playwright install --with-deps
```

#### Problem: Permission denied on browser installation
**Solution:**
```bash
# On macOS/Linux
sudo npx playwright install

# On Windows, run as Administrator
```

## Configuration Issues

### Environment Configuration

#### Problem: Tests fail with "Cannot read environment variables"
**Solution:**
1. Ensure `.env` file exists in project root
2. Copy from example: `cp .env.example .env`
3. Verify environment variables are set correctly

#### Problem: "BASE_URL is undefined"
**Solution:**
```bash
# Check .env file contains:
BASE_URL=https://qa4.employmentontario.labour.gov.on.ca

# Or set environment variable:
export BASE_URL=https://qa4.employmentontario.labour.gov.on.ca
```

### TypeScript Configuration

#### Problem: "Cannot find module" errors
**Solution:**
```bash
# Rebuild TypeScript
npm run build

# Check tsconfig.json paths configuration
# Ensure all imports use correct paths
```

#### Problem: Type errors in IDE
**Solution:**
1. Restart TypeScript service in IDE
2. Check `tsconfig.json` configuration
3. Ensure all dependencies are installed

## Test Execution Issues

### Browser Issues

#### Problem: "Browser not found" error
**Solution:**
```bash
# Reinstall browsers
npx playwright install chromium firefox webkit

# For specific browser
npx playwright install chromium
```

#### Problem: Browser crashes or hangs
**Solution:**
```bash
# Run in headed mode for debugging
npm run test:headed

# Increase timeouts in playwright.config.ts
timeout: 60000,
expect: { timeout: 10000 }
```

#### Problem: "Page crashed" errors
**Solution:**
1. Check system resources (memory, CPU)
2. Reduce parallel workers in config
3. Add browser launch options:
```typescript
use: {
  launchOptions: {
    args: ['--disable-dev-shm-usage', '--no-sandbox']
  }
}
```

### Network Issues

#### Problem: "net::ERR_CONNECTION_REFUSED"
**Solution:**
1. Verify application URL is accessible
2. Check firewall settings
3. Verify VPN connection if required
4. Test URL manually in browser

#### Problem: Slow network causing timeouts
**Solution:**
```typescript
// Increase timeouts in playwright.config.ts
use: {
  actionTimeout: 60000,
  navigationTimeout: 60000,
}
```

#### Problem: SSL certificate errors
**Solution:**
```typescript
// In playwright.config.ts
use: {
  ignoreHTTPSErrors: true,
}
```

### Element Selection Issues

#### Problem: "Element not found" errors
**Solution:**
1. Verify element exists on page
2. Check selector syntax
3. Add explicit waits:
```typescript
await page.waitForSelector(selector, { state: 'visible' });
```

#### Problem: "Element is not clickable"
**Solution:**
```typescript
// Wait for element to be enabled
await page.waitForSelector(selector, { state: 'visible' });
await page.locator(selector).waitFor({ state: 'attached' });

// Scroll into view
await page.locator(selector).scrollIntoViewIfNeeded();

// Use force click if necessary
await page.locator(selector).click({ force: true });
```

#### Problem: Flaky element interactions
**Solution:**
1. Use framework's safe methods:
```typescript
await this.helpers.safeClick(page, selector, 3); // 3 retries
await this.helpers.safeType(page, selector, text);
```

2. Add proper waits:
```typescript
await page.waitForLoadState('networkidle');
```

## Test Data Issues

### Data Loading Problems

#### Problem: "Test data file not found"
**Solution:**
1. Verify file exists in `src/data/` directory
2. Check file name and extension
3. Ensure proper JSON format

#### Problem: "Invalid JSON format"
**Solution:**
1. Validate JSON syntax using online validator
2. Check for trailing commas
3. Ensure proper quote usage

#### Problem: Random data generation fails
**Solution:**
```typescript
// Check DataManager implementation
const dataManager = new DataManager();
const data = dataManager.generateRandomUserData();

// Verify all required fields are generated
console.log(data);
```

## Reporting Issues

### Report Generation Problems

#### Problem: "No test results found"
**Solution:**
1. Ensure tests actually ran
2. Check output directory exists
3. Verify reporter configuration in `playwright.config.ts`

#### Problem: HTML report not opening
**Solution:**
```bash
# Manually open report
open reports/html-report/index.html

# Or use npm script
npm run test:report
```

#### Problem: Screenshots not captured
**Solution:**
1. Verify screenshot configuration:
```typescript
use: {
  screenshot: 'only-on-failure',
}
```

2. Check if screenshots directory exists and is writable

## Logging Issues

### Log File Problems

#### Problem: "Cannot write to log file"
**Solution:**
1. Ensure `logs/` directory exists
2. Check file permissions
3. Verify disk space

#### Problem: "Too many log files"
**Solution:**
```typescript
// Configure log rotation in logger.ts
new winston.transports.File({
  filename: 'logs/test.log',
  maxsize: 10 * 1024 * 1024, // 10MB
  maxFiles: 5,
})
```

## CI/CD Issues

### Azure DevOps Pipeline Problems

#### Problem: Pipeline fails at browser installation
**Solution:**
```yaml
# In azure-pipelines.yml
- script: |
    npx playwright install --with-deps chromium
  displayName: 'Install Playwright browsers'
```

#### Problem: Tests timeout in CI
**Solution:**
```yaml
# Increase timeout and reduce parallelism
variables:
  PARALLEL_WORKERS: '1'
  RETRY_COUNT: '3'
```

#### Problem: Artifacts not published
**Solution:**
1. Verify artifact paths in pipeline
2. Ensure directories exist before publishing
3. Check permissions

## Performance Issues

### Slow Test Execution

#### Problem: Tests running very slowly
**Solution:**
1. Reduce parallel workers if system is overloaded
2. Optimize selectors (use data-testid)
3. Remove unnecessary waits
4. Use `networkidle` sparingly

#### Problem: Memory issues
**Solution:**
```typescript
// In playwright.config.ts
use: {
  launchOptions: {
    args: ['--max-old-space-size=4096']
  }
}
```

### Resource Cleanup

#### Problem: Browser processes not closing
**Solution:**
1. Ensure proper test cleanup
2. Use `test.afterEach` for cleanup
3. Kill hanging processes manually if needed

## Debugging Techniques

### Debug Mode

```bash
# Run in debug mode
npm run test:debug

# Run specific test in debug mode
npx playwright test src/tests/specific.test.ts --debug
```

### UI Mode

```bash
# Run with Playwright UI
npm run test:ui
```

### Console Debugging

```typescript
// Add debug points in tests
await page.pause(); // Pauses execution

// Log page content
console.log(await page.content());

// Log element information
const element = page.locator(selector);
console.log(await element.getAttribute('class'));
```

### Screenshot Debugging

```typescript
// Take screenshots at specific points
await page.screenshot({ path: 'debug-screenshot.png' });

// Full page screenshot
await page.screenshot({ path: 'debug-full.png', fullPage: true });
```

## Getting Additional Help

### Log Analysis

1. Check `logs/test.log` for detailed execution logs
2. Look for error patterns
3. Check timestamps for performance issues

### Community Resources

1. [Playwright Documentation](https://playwright.dev/)
2. [Playwright GitHub Issues](https://github.com/microsoft/playwright/issues)
3. [Stack Overflow - Playwright](https://stackoverflow.com/questions/tagged/playwright)

### Internal Support

1. Check framework documentation in `docs/` folder
2. Review existing test examples
3. Contact the SDET team for framework-specific issues

### Reporting Bugs

When reporting issues, include:
1. Error message and stack trace
2. Test code that reproduces the issue
3. Environment details (OS, Node.js version, etc.)
4. Log files from `logs/` directory
5. Screenshots if UI-related

This troubleshooting guide should help resolve most common issues. If you encounter problems not covered here, please update this document with the solution for future reference.
